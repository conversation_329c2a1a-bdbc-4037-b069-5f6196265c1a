{"project_info": {"name": "Crestal Diamond", "type": "Streamlit Application", "language": "Python", "encoding": "utf-8"}, "paths": {"project_root": "C:\\Users\\<USER>\\Crestal Diamond", "gemini_cli_root": "C:\\Users\\<USER>", "main_app": "C:\\Users\\<USER>\\Crestal Diamond\\invoice_app.py", "pages_directory": "C:\\Users\\<USER>\\Crestal Diamond\\pages", "data_files": {"invoices": "C:\\Users\\<USER>\\Crestal Diamond\\invoices.csv", "gold_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\gold_inventory.csv", "diamond_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\diamond_inventory.csv"}, "memory_file": "C:\\Users\\<USER>\\Crestal Diamond\\project_memory.md", "paths_file": "C:\\Users\\<USER>\\Crestal Diamond\\project_paths.json", "collaboration_log": "C:\\Users\\<USER>\\Crestal Diamond\\collaboration_log.md"}, "pages": {"customers": "C:\\Users\\<USER>\\Crestal Diamond\\pages\\01_بيانات_العملاء.py", "gold_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\pages\\02_مخزون_الذهب.py", "diamond_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\pages\\03_مخزون_الألماس.py"}, "commands": {"run_streamlit": "streamlit run invoice_app.py", "gemini_cli": "C:\\Users\\<USER>\\gemini", "python": "python", "pip": "pip"}, "collaboration": {"agents": ["Augment Agent", "Gemini CLI", "Memory Agent"], "working_directory": "C:\\Users\\<USER>\\Crestal Diamond", "shared_memory": "C:\\Users\\<USER>\\Crestal Diamond\\project_memory.md"}}