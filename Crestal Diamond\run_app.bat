@echo off
chcp 65001 >nul
echo ========================================
echo    🚀 تشغيل تطبيق Crestal Diamond v2.0
echo ========================================
echo.
echo 💎 نظام إدارة ورشة المجوهرات المتطور
echo 📅 تاريخ الإصدار: 2025-07-10
echo 🔧 الإصدار: 2.0 - محسن ومطور
echo.

REM التحقق من وجود البيئة الافتراضية
if not exist "crestal_diamond_env\Scripts\activate.bat" (
    echo ❌ خطأ: البيئة الافتراضية غير موجودة
    echo يرجى تشغيل الأمر التالي أولاً:
    echo python -m venv crestal_diamond_env
    pause
    exit /b 1
)

REM تفعيل البيئة الافتراضية
echo 🔄 تفعيل البيئة الافتراضية...
call crestal_diamond_env\Scripts\activate.bat

REM التحقق من تثبيت المكتبات
echo 🔍 التحقق من المكتبات المطلوبة...
python -c "import streamlit, pandas; print('✅ جميع المكتبات الأساسية متوفرة')" 2>nul

if %errorlevel% neq 0 (
    echo ❌ خطأ: المكتبات غير مثبتة بشكل صحيح
    echo 📦 تثبيت المكتبات من requirements.txt...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

REM التحقق من وجود الملف الرئيسي
if not exist "app.py" (
    echo ❌ خطأ: ملف التطبيق الرئيسي غير موجود
    echo يرجى التأكد من وجود ملف app.py
    pause
    exit /b 1
)

echo.
echo ========================================
echo    🌟 بدء تشغيل التطبيق
echo ========================================
echo.
echo 🌐 التطبيق سيفتح في المتصفح على العنوان:
echo    📍 المحلي: http://localhost:8501
echo    📍 الشبكة: http://***********:8501
echo.
echo 💡 الميزات المتاحة:
echo    📄 إنشاء فواتير جديدة
echo    👥 إدارة حسابات العملاء
echo    📊 عرض الفواتير والتقارير
echo    ⚙️ إعدادات النظام
echo.
echo ⚠️  للإيقاف: اضغط Ctrl+C في هذه النافذة
echo.
echo 🚀 جاري تشغيل التطبيق...

REM تشغيل التطبيق
streamlit run app.py --server.headless false --server.port 8502 --server.runOnSave true

echo.
echo ========================================
echo    ✅ تم إيقاف التطبيق بنجاح
echo ========================================
echo.
echo 📊 إحصائيات الجلسة:
echo    🕐 وقت الإيقاف: %date% %time%
echo    💾 البيانات محفوظة في: invoices.csv
echo.
echo 💡 نصائح:
echo    • تأكد من عمل نسخة احتياطية دورية
echo    • راجع التقارير لمتابعة الأداء
echo    • استخدم صفحة الإعدادات للصيانة
echo.
echo شكراً لاستخدام Crestal Diamond! 💎
pause
