"""
🔄 تهجير مبسط للبيانات من CSV إلى MySQL - نظام كريستال دايموند
Simple Data Migration from CSV to MySQL for Crestal Diamond System
"""

import pandas as pd
import os
from database_config import execute_query
from datetime import datetime, date
import logging

logger = logging.getLogger(__name__)

def migrate_invoices_simple():
    """تهجير بيانات الفواتير بتنسيق مبسط"""
    filename = '../invoices.csv'
    if not os.path.exists(filename):
        logger.warning(f"⚠️ ملف الفواتير غير موجود: {filename}")
        return False
    
    try:
        df = pd.read_csv(filename)
        logger.info(f"📊 تم قراءة {len(df)} سجل من ملف الفواتير")
        
        migrated_count = 0
        for index, row in df.iterrows():
            # إنشاء رقم فاتورة فريد
            invoice_number = f'INV-{datetime.now().strftime("%Y%m%d")}-{index+1:04d}'
            
            # تحويل التاريخ
            try:
                invoice_date = pd.to_datetime(row.get('date', datetime.now())).date()
            except:
                invoice_date = date.today()
            
            # تحضير البيانات
            customer_name = row.get('customer_name', 'عميل غير محدد')
            description = row.get('description', '')
            gold_change = float(row.get('gold_change', 0) or 0)
            usd_change = float(row.get('usd_change', 0) or 0)
            egp_change = float(row.get('egp_change', 0) or 0)
            
            # إدراج البيانات في قاعدة البيانات
            query = """
            INSERT INTO invoices (
                invoice_number, customer_name, main_product,
                main_product_price_usd, main_product_price_egp,
                total_usd, total_egp, invoice_date, status, notes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            params = (
                invoice_number,
                customer_name,
                description,
                usd_change,
                egp_change,
                usd_change,
                egp_change,
                invoice_date,
                'completed',
                f'تم التهجير من CSV - تغيير الذهب: {gold_change} جرام'
            )
            
            if execute_query(query, params):
                migrated_count += 1
                logger.info(f"✅ تم تهجير الفاتورة: {invoice_number}")
            else:
                logger.error(f"❌ فشل في تهجير الفاتورة: {invoice_number}")
        
        logger.info(f"✅ تم تهجير {migrated_count} فاتورة بنجاح")
        return migrated_count > 0
        
    except Exception as e:
        logger.error(f"❌ خطأ في تهجير الفواتير: {e}")
        return False

def migrate_inventory_simple():
    """تهجير بيانات المخزون بتنسيق مبسط"""
    filename = '../gold_inventory.csv'
    if not os.path.exists(filename):
        logger.warning(f"⚠️ ملف المخزون غير موجود: {filename}")
        return False
    
    try:
        df = pd.read_csv(filename)
        logger.info(f"📊 تم قراءة {len(df)} عنصر من ملف المخزون")
        
        migrated_count = 0
        for _, row in df.iterrows():
            # تحضير البيانات
            item_name = row.get('item_name', 'عنصر غير محدد')
            category = row.get('category', 'ذهب')
            quantity = int(row.get('quantity', 0) or 0)
            unit_price_usd = float(row.get('unit_price_usd', 0) or 0)
            unit_price_egp = float(row.get('unit_price_egp', 0) or 0)
            
            # حساب القيمة الإجمالية
            total_value_usd = quantity * unit_price_usd
            total_value_egp = quantity * unit_price_egp
            
            # إدراج البيانات في قاعدة البيانات
            query = """
            INSERT INTO inventory (
                item_name, category, quantity, unit_price_usd, unit_price_egp,
                total_value_usd, total_value_egp, notes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            params = (
                item_name,
                category,
                quantity,
                unit_price_usd,
                unit_price_egp,
                total_value_usd,
                total_value_egp,
                'تم التهجير من CSV'
            )
            
            if execute_query(query, params):
                migrated_count += 1
                logger.info(f"✅ تم تهجير عنصر المخزون: {item_name}")
            else:
                logger.error(f"❌ فشل في تهجير عنصر المخزون: {item_name}")
        
        logger.info(f"✅ تم تهجير {migrated_count} عنصر مخزون بنجاح")
        return migrated_count > 0
        
    except Exception as e:
        logger.error(f"❌ خطأ في تهجير المخزون: {e}")
        return False

def create_sample_customers():
    """إنشاء عملاء نموذجيين من بيانات الفواتير"""
    try:
        # جلب أسماء العملاء الفريدة من الفواتير
        query = "SELECT DISTINCT customer_name FROM invoices WHERE customer_name IS NOT NULL"
        result = execute_query(query, fetch=True)
        
        if not result:
            logger.warning("⚠️ لا توجد أسماء عملاء في الفواتير")
            return False
        
        migrated_count = 0
        for row in result:
            customer_name = row['customer_name']
            
            # إدراج العميل في جدول العملاء
            query = """
            INSERT IGNORE INTO customers (customer_name, notes)
            VALUES (%s, %s)
            """
            
            params = (customer_name, 'تم إنشاؤه تلقائياً من بيانات الفواتير')
            
            if execute_query(query, params):
                migrated_count += 1
                logger.info(f"✅ تم إنشاء العميل: {customer_name}")
        
        logger.info(f"✅ تم إنشاء {migrated_count} عميل بنجاح")
        return migrated_count > 0
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء العملاء: {e}")
        return False

def run_simple_migration():
    """تشغيل التهجير المبسط"""
    logger.info("🚀 بدء التهجير المبسط...")
    
    results = {}
    
    # تهجير الفواتير
    results['invoices'] = migrate_invoices_simple()
    
    # تهجير المخزون
    results['inventory'] = migrate_inventory_simple()
    
    # إنشاء العملاء من الفواتير
    results['customers'] = create_sample_customers()
    
    # تقرير النتائج
    successful = sum(results.values())
    total = len(results)
    
    logger.info(f"📊 تقرير التهجير المبسط: {successful}/{total} نجح")
    
    if successful > 0:
        logger.info("🎉 تم التهجير بنجاح!")
        return True
    else:
        logger.warning("⚠️ لم يتم تهجير أي بيانات")
        return False

if __name__ == "__main__":
    # إعداد السجلات
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # تشغيل التهجير
    success = run_simple_migration()
    
    if success:
        print("\n🏁 انتهى التهجير بنجاح!")
    else:
        print("\n🚨 انتهى التهجير مع وجود مشاكل!")
