# Crestal Diamond - متطلبات المشروع
# تم إنشاؤه بواسطة النظام التعاوني

# المكتبات الأساسية
streamlit>=1.46.0
pandas>=2.3.0
numpy>=1.23.0

# مكتبات معالجة البيانات
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# مكتبات التاريخ والوقت
python-dateutil>=2.8.0

# مكتبات الواجهة والرسوم البيانية
plotly>=5.17.0
matplotlib>=3.7.0
seaborn>=0.12.0

# مكتبات الأمان والتشفير
cryptography>=41.0.0

# مكتبات إدارة الملفات
pathlib2>=2.3.0

# مكتبات التحقق من البيانات
pydantic>=2.5.0

# مكتبات اللغة العربية
arabic-reshaper>=3.0.0
python-bidi>=0.4.0

# مكتبات إضافية لـ Streamlit
streamlit-option-menu>=0.3.0
streamlit-aggrid>=0.3.0

# مكتبات التطوير والاختبار (اختيارية)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
