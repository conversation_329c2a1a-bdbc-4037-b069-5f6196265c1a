{"project_info": {"name": "Crestal Diamond", "type": "Streamlit Application", "language": "Python", "encoding": "utf-8", "created_date": "2025-07-10", "team": ["Augment Agent", "Gemini CLI", "Memory Agent"]}, "paths": {"project_root": "C:\\Users\\<USER>\\Crestal Diamond", "gemini_cli_root": "C:\\Users\\<USER>", "collaborative_workspace": "C:\\Users\\<USER>\\Crestal Diamond\\Collaborative_Workspace", "main_app": "C:\\Users\\<USER>\\Crestal Diamond\\invoice_app.py", "pages_directory": "C:\\Users\\<USER>\\Crestal Diamond\\pages", "data_files": {"invoices": "C:\\Users\\<USER>\\Crestal Diamond\\invoices.csv", "gold_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\gold_inventory.csv", "diamond_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\diamond_inventory.csv"}, "collaboration_files": {"memory_file": "C:\\Users\\<USER>\\Crestal Diamond\\Collaborative_Workspace\\shared_memory.md", "paths_file": "C:\\Users\\<USER>\\Crestal Diamond\\Collaborative_Workspace\\project_paths.json", "collaboration_log": "C:\\Users\\<USER>\\Crestal Diamond\\Collaborative_Workspace\\collaboration_log.md", "task_management": "C:\\Users\\<USER>\\Crestal Diamond\\Collaborative_Workspace\\task_management.md", "analysis_report": "C:\\Users\\<USER>\\Crestal Diamond\\Collaborative_Workspace\\analysis_report.md"}}, "pages": {"customers": "C:\\Users\\<USER>\\Crestal Diamond\\pages\\01_بيانات_العملاء.py", "gold_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\pages\\02_مخزون_الذهب.py", "diamond_inventory": "C:\\Users\\<USER>\\Crestal Diamond\\pages\\03_مخزون_الألماس.py"}, "commands": {"run_streamlit": "streamlit run invoice_app.py", "gemini_cli_command": "gemini", "python": "python", "pip": "pip", "change_to_project": "cd \"C:\\Users\\<USER>\\Crestal Diamond\"", "change_to_gemini": "cd \"C:\\Users\\<USER>"}, "collaboration_workflow": {"step1": "Augment Agent: Initial analysis and file structure review", "step2": "Gemini CLI: Deep code analysis and testing", "step3": "Memory Agent: Documentation and knowledge management", "step4": "Joint: Problem solving and implementation", "step5": "All: Testing and validation"}}