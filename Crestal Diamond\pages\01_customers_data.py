import streamlit as st
import pandas as pd
import os
import sys
from datetime import datetime, date

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

# استيراد وحدات قاعدة البيانات
try:
    from database.database_operations import customer_ops, transaction_ops
    from database.database_config import test_connection
    DATABASE_AVAILABLE = True
except ImportError as e:
    st.error(f"خطأ في تحميل قاعدة البيانات: {e}")
    DATABASE_AVAILABLE = False

st.set_page_config(layout="wide", page_title="حسابات العملاء - Crestal Diamond")
st.title("📊 مركز التحكم بحسابات العملاء - MySQL")

# --- دالة لحفظ أي حركة جديدة في قاعدة البيانات ---
def save_transaction_db(customer, transaction_date, desc, amount_usd, amount_egp, transaction_type):
    """حفظ معاملة في قاعدة البيانات MySQL"""
    try:
        if not DATABASE_AVAILABLE:
            st.error("قاعدة البيانات غير متاحة")
            return False

        transaction_data = {
            'customer_name': customer,
            'transaction_date': transaction_date,
            'description': desc,
            'amount_usd': amount_usd,
            'amount_egp': amount_egp,
            'transaction_type': transaction_type
        }

        return transaction_ops.add_transaction(transaction_data)
    except Exception as e:
        st.error(f"خطأ في حفظ المعاملة: {str(e)}")
        return False

# --- عرض البيانات من قاعدة البيانات ---
if not DATABASE_AVAILABLE:
    st.error("❌ قاعدة البيانات غير متاحة")
    st.info("يرجى التأكد من تشغيل خادم MySQL وصحة الاتصال")
else:
    # اختبار الاتصال
    if not test_connection():
        st.error("❌ فشل في الاتصال بقاعدة البيانات")
        st.stop()

    # جلب قائمة العملاء
    customers_df = customer_ops.get_all_customers()

    if customers_df.empty:
        st.warning("لم يتم تسجيل أي عملاء بعد. يرجى إضافة عميل أولاً.")

        # إضافة قسم لإضافة عميل جديد
        with st.expander("➕ إضافة عميل جديد"):
            with st.form("add_customer_form"):
                st.subheader("بيانات العميل الجديد")
                col1, col2 = st.columns(2)

                with col1:
                    new_customer_name = st.text_input("اسم العميل *")
                    new_customer_phone = st.text_input("رقم الهاتف")

                with col2:
                    new_customer_email = st.text_input("البريد الإلكتروني")
                    new_customer_address = st.text_area("العنوان")

                new_customer_notes = st.text_area("ملاحظات")

                if st.form_submit_button("إضافة العميل", type="primary"):
                    if new_customer_name.strip():
                        customer_data = {
                            'customer_name': new_customer_name.strip(),
                            'phone': new_customer_phone,
                            'email': new_customer_email,
                            'address': new_customer_address,
                            'notes': new_customer_notes
                        }

                        if customer_ops.add_customer(customer_data):
                            st.success(f"✅ تم إضافة العميل: {new_customer_name}")
                            st.rerun()
                        else:
                            st.error("❌ فشل في إضافة العميل")
                    else:
                        st.error("❌ يرجى إدخال اسم العميل")
    else:
        customer_list = customers_df['customer_name'].tolist()
        selected_customer = st.selectbox("اختر العميل:", customer_list)
    
    if selected_customer:
        # --- قسم إضافة حركة جديدة (رصيد، دفعة، تسوية) ---
        with st.expander("➕ إضافة حركة جديدة لحساب العميل"):
            with st.form("transaction_form", clear_on_submit=True):
                st.write("**تسجيل رصيد افتتاحي أو دفعة أو تسوية**")
                t_type = st.selectbox("نوع الحركة:", ["دفعة من العميل", "رصيد افتتاحي", "تسوية / خصم"])
                t_date = st.date_input("تاريخ الحركة")
                t_desc = st.text_input("بيان الحركة (مثال: دفعة كاش)")
                
                c1, c2, c3 = st.columns(3)
                with c1:
                    t_gold = st.number_input("ذهب (جرام)", format="%.2f", key="t_gold")
                with c2:
                    t_usd = st.number_input("دولار ($)", format="%.2f", key="t_usd")
                with c3:
                    t_egp = st.number_input("جنيه (EGP)", format="%.2f", key="t_egp")
                
                submitted = st.form_submit_button("حفظ الحركة")
                if submitted:
                    # تحديد نوع المعاملة وقيمها
                    if t_type in ["دفعة من العميل", "تسوية / خصم"]:
                        final_usd, final_egp = -abs(t_usd), -abs(t_egp)
                        transaction_type = "payment" if t_type == "دفعة من العميل" else "adjustment"
                    else:  # رصيد افتتاحي
                        final_usd, final_egp = abs(t_usd), abs(t_egp)
                        transaction_type = "opening_balance"

                    # حفظ المعاملة في قاعدة البيانات
                    success = save_transaction_db(
                        customer=selected_customer,
                        transaction_date=t_date,
                        desc=t_desc or t_type,
                        amount_usd=final_usd,
                        amount_egp=final_egp,
                        transaction_type=transaction_type
                    )

                    if success:
                        st.success(f"✅ تم حفظ الحركة '{t_type}' لحساب {selected_customer}")
                        st.rerun()  # لإعادة تحميل البيانات وعرضها فوراً
                    else:
                        st.error("❌ فشل في حفظ الحركة")

        # --- قسم عرض الحساب من قاعدة البيانات ---
        st.write("---")
        st.header(f"كشف حساب: {selected_customer}")

        # جلب معاملات العميل من قاعدة البيانات
        try:
            transactions_df = transaction_ops.get_customer_transactions(selected_customer)

            if not transactions_df.empty:
                # حساب الأرصدة
                total_usd = transactions_df['amount_usd'].sum()
                total_egp = transactions_df['amount_egp'].sum()

                # عرض الأرصدة
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("رصيد الدولار النهائي", f"$ {total_usd:.2f}")
                with col2:
                    st.metric("رصيد الجنيه النهائي", f"{total_egp:.2f} ج.م")
                with col3:
                    total_transactions = len(transactions_df)
                    st.metric("إجمالي المعاملات", total_transactions)

                st.write("---")

                # --- عرض بيان العمليات ---
                st.subheader("📋 بيان العمليات")

                # تنسيق البيانات للعرض
                display_df = transactions_df.copy()
                display_df['transaction_date'] = pd.to_datetime(display_df['transaction_date']).dt.strftime('%Y-%m-%d')
                display_df = display_df.sort_values('transaction_date', ascending=False)

                # إعادة تسمية الأعمدة للعرض
                display_df = display_df.rename(columns={
                    'transaction_date': 'التاريخ',
                    'description': 'البيان',
                    'amount_usd': 'المبلغ (USD)',
                    'amount_egp': 'المبلغ (EGP)',
                    'transaction_type': 'نوع المعاملة'
                })

                # عرض الجدول
                st.dataframe(
                    display_df[['التاريخ', 'البيان', 'المبلغ (USD)', 'المبلغ (EGP)', 'نوع المعاملة']],
                    use_container_width=True
                )

                # إحصائيات إضافية
                st.subheader("📊 إحصائيات العميل")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    invoices_count = len(transactions_df[transactions_df['transaction_type'] == 'invoice'])
                    st.metric("عدد الفواتير", invoices_count)

                with col2:
                    payments_count = len(transactions_df[transactions_df['transaction_type'] == 'payment'])
                    st.metric("عدد الدفعات", payments_count)

                with col3:
                    total_invoices_usd = transactions_df[transactions_df['transaction_type'] == 'invoice']['amount_usd'].sum()
                    st.metric("إجمالي الفواتير (USD)", f"$ {total_invoices_usd:.2f}")

                with col4:
                    total_payments_usd = abs(transactions_df[transactions_df['transaction_type'] == 'payment']['amount_usd'].sum())
                    st.metric("إجمالي الدفعات (USD)", f"$ {total_payments_usd:.2f}")

            else:
                st.info("لا توجد معاملات لعرضها لهذا العميل.")

        except Exception as e:
            st.error(f"خطأ في جلب بيانات العميل: {str(e)}")