import streamlit as st
import pandas as pd
import os
from datetime import datetime

st.set_page_config(layout="wide", page_title="حسابات العملاء")
st.title("📊 مركز التحكم بحسابات العملاء")

# --- دالة لحفظ أي حركة جديدة ---
def save_transaction(customer, date, desc, gold, usd, egp):
    columns = ["customer_name", "date", "description", "gold_change", "usd_change", "egp_change"]
    new_data = pd.DataFrame([[customer, date, desc, gold, usd, egp]], columns=columns)
    file_path = 'invoices.csv'
    if not os.path.isfile(file_path):
        new_data.to_csv(file_path, index=False, encoding='utf-8-sig')
    else:
        new_data.to_csv(file_path, mode='a', header=False, index=False, encoding='utf-8-sig')

# --- عرض البيانات ---
file_path = 'invoices.csv'
if not os.path.isfile(file_path):
    st.warning("لم يتم تسجيل أي فواتير بعد. يرجى إضافة فاتورة من الصفحة الرئيسية أولاً.")
else:
    df = pd.read_csv(file_path)
    # التأكد من أن أسماء العملاء فريدة وغير فارغة
    customer_list = [c for c in df['customer_name'].unique() if pd.notna(c)]
    
    selected_customer = st.selectbox("اختر العميل:", customer_list)
    
    if selected_customer:
        # --- قسم إضافة حركة جديدة (رصيد، دفعة، تسوية) ---
        with st.expander("➕ إضافة حركة جديدة لحساب العميل"):
            with st.form("transaction_form", clear_on_submit=True):
                st.write("**تسجيل رصيد افتتاحي أو دفعة أو تسوية**")
                t_type = st.selectbox("نوع الحركة:", ["دفعة من العميل", "رصيد افتتاحي", "تسوية / خصم"])
                t_date = st.date_input("تاريخ الحركة")
                t_desc = st.text_input("بيان الحركة (مثال: دفعة كاش)")
                
                c1, c2, c3 = st.columns(3)
                with c1:
                    t_gold = st.number_input("ذهب (جرام)", format="%.2f", key="t_gold")
                with c2:
                    t_usd = st.number_input("دولار ($)", format="%.2f", key="t_usd")
                with c3:
                    t_egp = st.number_input("جنيه (EGP)", format="%.2f", key="t_egp")
                
                submitted = st.form_submit_button("حفظ الحركة")
                if submitted:
                    # لو الحركة دفعة أو تسوية، نجعل الأرقام بالسالب
                    if t_type in ["دفعة من العميل", "تسوية / خصم"]:
                        t_gold, t_usd, t_egp = -abs(t_gold), -abs(t_usd), -abs(t_egp)
                    else: # رصيد افتتاحي
                         t_gold, t_usd, t_egp = abs(t_gold), abs(t_usd), abs(t_egp)

                    save_transaction(selected_customer, str(t_date), t_desc, t_gold, t_usd, t_egp)
                    st.success(f"تم حفظ الحركة '{t_type}' لحساب {selected_customer}")
                    # st.rerun() # لإعادة تحميل البيانات وعرضها فوراً

        # --- قسم عرض الحساب ---
        st.write("---")
        st.header(f"كشف حساب: {selected_customer}")
        
        # إعادة قراءة الملف بعد أي إضافة محتملة
        df = pd.read_csv(file_path)
        customer_df = df[df['customer_name'] == selected_customer].copy()
        
        if not customer_df.empty:
            total_gold = customer_df['gold_change'].sum()
            total_usd = customer_df['usd_change'].sum()
            total_egp = customer_df['egp_change'].sum()
            
            col1, col2, col3 = st.columns(3)
            col1.metric("رصيد الذهب النهائي", f"{total_gold:.2f} جرام")
            col2.metric("رصيد الدولار النهائي", f"$ {total_usd:.2f}")
            col3.metric("رصيد الجنيه النهائي", f"{total_egp:.2f} ج.م")
            
            st.write("---")
            
            # --- عرض بيان العمليات (سجل الفواتير) ---
            st.subheader("بيان العمليات")
            st.dataframe(customer_df.sort_values('date', ascending=False), use_container_width=True)
        else:
            st.info("لا توجد بيانات لعرضها لهذا العميل.")