"""
🤖 واجهة الوكيل الذكي لقاعدة البيانات - نظام كريستال دايموند
Intelligent Database Agent Interface for Crestal Diamond System
"""

import streamlit as st
import sys
import os
from datetime import datetime
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

try:
    from database.intelligent_db_agent import db_agent
    AGENT_AVAILABLE = True
except ImportError as e:
    st.error(f"خطأ في تحميل الوكيل الذكي: {e}")
    AGENT_AVAILABLE = False

st.set_page_config(
    page_title="الوكيل الذكي لقاعدة البيانات - Crestal Diamond",
    page_icon="🤖",
    layout="wide"
)

def main():
    """الواجهة الرئيسية للوكيل الذكي"""
    
    # العنوان الرئيسي
    st.title("🤖 الوكيل الذكي لقاعدة البيانات")
    st.markdown("**مراقبة وصيانة وتحسين قاعدة البيانات تلقائياً**")
    
    if not AGENT_AVAILABLE:
        st.error("❌ الوكيل الذكي غير متاح")
        st.info("يرجى التأكد من تثبيت جميع المتطلبات وصحة إعداد قاعدة البيانات")
        return
    
    # شريط جانبي للتحكم
    with st.sidebar:
        st.header("🎛️ لوحة التحكم")
        
        # أزرار التحكم الرئيسية
        if st.button("🔍 فحص صحة شامل", type="primary"):
            st.session_state.run_health_check = True
        
        if st.button("📊 تحديث الإحصائيات"):
            st.session_state.update_stats = True
        
        if st.button("🔧 تطبيق إصلاحات تلقائية"):
            st.session_state.apply_fixes = True
        
        if st.button("💾 حفظ تقرير"):
            st.session_state.save_report = True
        
        st.divider()
        
        # إعدادات المراقبة
        st.subheader("⚙️ إعدادات المراقبة")
        
        auto_monitor = st.checkbox("مراقبة تلقائية", value=False)
        if auto_monitor:
            monitor_interval = st.slider("فترة المراقبة (دقائق)", 5, 120, 30)
            st.info(f"المراقبة التلقائية كل {monitor_interval} دقيقة")
        
        st.divider()
        
        # معلومات الوكيل
        st.subheader("ℹ️ معلومات الوكيل")
        st.info("""
        **الوكيل الذكي يقوم بـ:**
        - فحص صحة قاعدة البيانات
        - اكتشاف وإصلاح المشاكل
        - تحسين الأداء
        - مراقبة الأمان
        - إنشاء تقارير مفصلة
        """)
    
    # المحتوى الرئيسي
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # لوحة معلومات الحالة
        display_status_dashboard()
        
        # تنفيذ العمليات المطلوبة
        handle_user_actions()
        
        # عرض التقارير
        display_reports()
    
    with col2:
        # إحصائيات سريعة
        display_quick_stats()
        
        # سجل الأنشطة
        display_activity_log()

def display_status_dashboard():
    """عرض لوحة معلومات الحالة"""
    st.header("📊 لوحة معلومات الحالة")
    
    # بطاقات الحالة
    col1, col2, col3, col4 = st.columns(4)
    
    try:
        # فحص سريع للحالة
        connection_status = db_agent._check_connection()
        
        with col1:
            status_color = "🟢" if connection_status else "🔴"
            st.metric(
                "حالة الاتصال",
                f"{status_color} {'متصل' if connection_status else 'منقطع'}",
                delta="طبيعي" if connection_status else "خطأ"
            )
        
        with col2:
            last_check = db_agent.last_check_time
            if last_check:
                time_diff = datetime.now() - last_check
                minutes_ago = int(time_diff.total_seconds() / 60)
                st.metric("آخر فحص", f"منذ {minutes_ago} دقيقة")
            else:
                st.metric("آخر فحص", "لم يتم بعد")
        
        with col3:
            issues_count = len(db_agent.issues_found)
            st.metric(
                "المشاكل المكتشفة",
                issues_count,
                delta="يحتاج انتباه" if issues_count > 0 else "سليم"
            )
        
        with col4:
            fixes_count = len(db_agent.fixes_applied)
            st.metric("الإصلاحات المطبقة", fixes_count)
    
    except Exception as e:
        st.error(f"خطأ في عرض الحالة: {str(e)}")

def display_quick_stats():
    """عرض إحصائيات سريعة"""
    st.header("📈 إحصائيات سريعة")
    
    try:
        from database.database_operations import customer_ops, invoice_ops, inventory_ops, transaction_ops
        
        # جلب الإحصائيات
        customers_count = len(customer_ops.get_all_customers())
        invoices_count = len(invoice_ops.get_all_invoices())
        inventory_count = len(inventory_ops.get_all_inventory())
        transactions_count = len(transaction_ops.get_all_transactions())
        
        # عرض الإحصائيات
        stats_data = {
            "الجدول": ["العملاء", "الفواتير", "المخزون", "المعاملات"],
            "عدد السجلات": [customers_count, invoices_count, inventory_count, transactions_count],
            "الأيقونة": ["👥", "🧾", "📦", "💳"]
        }
        
        for i, (table, count, icon) in enumerate(zip(stats_data["الجدول"], stats_data["عدد السجلات"], stats_data["الأيقونة"])):
            st.metric(f"{icon} {table}", f"{count:,}")
        
        # رسم بياني للإحصائيات
        fig = px.bar(
            x=stats_data["الجدول"],
            y=stats_data["عدد السجلات"],
            title="توزيع البيانات",
            color=stats_data["عدد السجلات"],
            color_continuous_scale="viridis"
        )
        fig.update_layout(height=300, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"خطأ في جلب الإحصائيات: {str(e)}")

def display_activity_log():
    """عرض سجل الأنشطة"""
    st.header("📝 سجل الأنشطة")
    
    try:
        # قراءة سجل الأنشطة من ملف السجل
        log_file = "database_agent.log"
        
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                log_lines = f.readlines()
            
            # عرض آخر 10 أسطر
            recent_logs = log_lines[-10:] if len(log_lines) > 10 else log_lines
            
            for log_line in reversed(recent_logs):
                if log_line.strip():
                    # تنسيق السجل
                    if "ERROR" in log_line:
                        st.error(log_line.strip())
                    elif "WARNING" in log_line:
                        st.warning(log_line.strip())
                    elif "INFO" in log_line:
                        st.info(log_line.strip())
                    else:
                        st.text(log_line.strip())
        else:
            st.info("لا يوجد سجل أنشطة بعد")
    
    except Exception as e:
        st.error(f"خطأ في عرض سجل الأنشطة: {str(e)}")

def handle_user_actions():
    """التعامل مع إجراءات المستخدم"""
    
    # فحص صحة شامل
    if st.session_state.get('run_health_check', False):
        st.session_state.run_health_check = False
        
        with st.spinner("🔍 جاري إجراء فحص صحة شامل..."):
            try:
                health_report = db_agent.comprehensive_health_check()
                
                st.success("✅ تم إكمال الفحص الصحي الشامل")
                
                # عرض نتائج الفحص
                col1, col2 = st.columns(2)
                
                with col1:
                    st.subheader("📊 نتائج الفحص")
                    st.json(health_report)
                
                with col2:
                    st.subheader("🎯 الحالة العامة")
                    status = health_report.get('overall_status', 'unknown')
                    
                    if status == 'healthy':
                        st.success("🟢 النظام سليم")
                    elif status == 'needs_attention':
                        st.warning("🟡 يحتاج انتباه")
                    else:
                        st.error("🔴 يوجد مشاكل")
                
            except Exception as e:
                st.error(f"❌ خطأ في الفحص: {str(e)}")
    
    # تطبيق إصلاحات تلقائية
    if st.session_state.get('apply_fixes', False):
        st.session_state.apply_fixes = False
        
        with st.spinner("🔧 جاري تطبيق الإصلاحات التلقائية..."):
            try:
                fixes = db_agent._apply_auto_fixes()
                
                if fixes:
                    st.success(f"✅ تم تطبيق {len(fixes)} إصلاح")
                    for fix in fixes:
                        st.info(f"🔧 {fix}")
                else:
                    st.info("ℹ️ لا توجد إصلاحات مطلوبة")
                
            except Exception as e:
                st.error(f"❌ خطأ في تطبيق الإصلاحات: {str(e)}")
    
    # حفظ تقرير
    if st.session_state.get('save_report', False):
        st.session_state.save_report = False
        
        try:
            filename = db_agent.save_report_to_file()
            if filename:
                st.success(f"✅ تم حفظ التقرير في: {filename}")
                
                # إمكانية تحميل التقرير
                with open(filename, 'r', encoding='utf-8') as f:
                    report_content = f.read()
                
                st.download_button(
                    label="📥 تحميل التقرير",
                    data=report_content,
                    file_name=filename,
                    mime="text/plain"
                )
            else:
                st.error("❌ فشل في حفظ التقرير")
                
        except Exception as e:
            st.error(f"❌ خطأ في حفظ التقرير: {str(e)}")

def display_reports():
    """عرض التقارير المفصلة"""
    st.header("📋 التقارير المفصلة")
    
    # تبويبات للتقارير المختلفة
    tab1, tab2, tab3, tab4 = st.tabs(["📊 تقرير الحالة", "🔍 تفاصيل الفحص", "🔧 الإصلاحات", "💡 التوصيات"])
    
    with tab1:
        if st.button("🔄 تحديث تقرير الحالة"):
            try:
                report = db_agent.generate_health_report()
                st.text_area("تقرير الحالة الشامل", report, height=400)
            except Exception as e:
                st.error(f"خطأ في إنشاء التقرير: {str(e)}")
    
    with tab2:
        st.subheader("🔍 تفاصيل آخر فحص")
        
        if db_agent.health_status:
            # عرض تفاصيل الفحص
            health_data = db_agent.health_status
            
            # حالة الاتصال
            st.metric("حالة الاتصال", "✅ متصل" if health_data.get('connection_status') else "❌ منقطع")
            
            # سلامة المخطط
            schema_integrity = health_data.get('schema_integrity', {})
            if schema_integrity:
                st.subheader("🗄️ سلامة المخطط")
                st.json(schema_integrity)
            
            # صحة البيانات
            data_validation = health_data.get('data_validation', {})
            if data_validation:
                st.subheader("✅ صحة البيانات")
                
                # إنشاء جدول للنتائج
                validation_df = pd.DataFrame([
                    {
                        'الجدول': table,
                        'السجلات الصحيحة': data.get('valid', 0),
                        'السجلات الخاطئة': data.get('invalid', 0),
                        'عدد المشاكل': len(data.get('issues', []))
                    }
                    for table, data in data_validation.items()
                ])
                
                st.dataframe(validation_df, use_container_width=True)
        else:
            st.info("لم يتم إجراء فحص بعد. اضغط على 'فحص صحة شامل' لبدء الفحص.")
    
    with tab3:
        st.subheader("🔧 الإصلاحات المطبقة")
        
        if db_agent.fixes_applied:
            for i, fix in enumerate(db_agent.fixes_applied, 1):
                st.success(f"{i}. {fix}")
        else:
            st.info("لم يتم تطبيق أي إصلاحات بعد")
    
    with tab4:
        st.subheader("💡 التوصيات")
        
        recommendations = db_agent._generate_recommendations()
        
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                st.info(f"{i}. {rec}")
        else:
            st.success("لا توجد توصيات إضافية - النظام يعمل بشكل مثالي!")

# تشغيل التطبيق
if __name__ == "__main__":
    main()
