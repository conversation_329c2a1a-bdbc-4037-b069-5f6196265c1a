@echo off
echo ========================================
echo    Gemini CLI Commands for Crestal Diamond
echo ========================================
echo.

REM تعيين المسارات
set PROJECT_PATH=C:\Users\<USER>\Crestal Diamond
set GEMINI_PATH=C:\Users\<USER>\Collaborative_Workspace

echo Current Project Path: %PROJECT_PATH%
echo Gemini CLI Path: %GEMINI_PATH%
echo Workspace Path: %WORKSPACE_PATH%
echo.

REM الانتقال لمجلد المشروع
cd /d "%PROJECT_PATH%"
echo Changed to project directory: %cd%
echo.

echo ========================================
echo    Available Gemini CLI Commands
echo ========================================
echo 1. Analyze main application file
echo 2. Analyze all Python files
echo 3. Test application startup
echo 4. Security check
echo 5. Performance analysis
echo 6. Code quality check
echo 7. Generate documentation
echo 8. Custom command
echo 9. Exit
echo.

:menu
set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto analyze_main
if "%choice%"=="2" goto analyze_all
if "%choice%"=="3" goto test_app
if "%choice%"=="4" goto security_check
if "%choice%"=="5" goto performance_check
if "%choice%"=="6" goto quality_check
if "%choice%"=="7" goto generate_docs
if "%choice%"=="8" goto custom_command
if "%choice%"=="9" goto exit
echo Invalid choice. Please try again.
goto menu

:analyze_main
echo.
echo ========================================
echo    Analyzing Main Application File
echo ========================================
cd /d "%GEMINI_PATH%"
gemini analyze "%PROJECT_PATH%\invoice_app.py" --output "%WORKSPACE_PATH%\analysis_main.md" --format markdown
echo Analysis complete. Results saved to analysis_main.md
echo.
goto menu

:analyze_all
echo.
echo ========================================
echo    Analyzing All Python Files
echo ========================================
cd /d "%GEMINI_PATH%"
gemini analyze "%PROJECT_PATH%\*.py" --recursive --output "%WORKSPACE_PATH%\analysis_all.md" --format markdown
gemini analyze "%PROJECT_PATH%\pages\*.py" --output "%WORKSPACE_PATH%\analysis_pages.md" --format markdown
echo Analysis complete. Results saved to analysis files.
echo.
goto menu

:test_app
echo.
echo ========================================
echo    Testing Application Startup
echo ========================================
cd /d "%PROJECT_PATH%"
echo Testing Streamlit application...
timeout /t 2 /nobreak > nul
start /min streamlit run invoice_app.py --server.headless true --server.port 8501
echo Application started on port 8501
echo Check http://localhost:8501 to verify
echo Press any key to stop the application...
pause > nul
taskkill /f /im streamlit.exe > nul 2>&1
echo Application stopped.
echo.
goto menu

:security_check
echo.
echo ========================================
echo    Security Check
echo ========================================
cd /d "%GEMINI_PATH%"
gemini security-scan "%PROJECT_PATH%" --output "%WORKSPACE_PATH%\security_report.md" --format markdown
echo Security check complete. Report saved to security_report.md
echo.
goto menu

:performance_check
echo.
echo ========================================
echo    Performance Analysis
echo ========================================
cd /d "%GEMINI_PATH%"
gemini performance "%PROJECT_PATH%\invoice_app.py" --output "%WORKSPACE_PATH%\performance_report.md"
echo Performance analysis complete. Report saved to performance_report.md
echo.
goto menu

:quality_check
echo.
echo ========================================
echo    Code Quality Check
echo ========================================
cd /d "%GEMINI_PATH%"
gemini quality "%PROJECT_PATH%" --output "%WORKSPACE_PATH%\quality_report.md" --format markdown
echo Quality check complete. Report saved to quality_report.md
echo.
goto menu

:generate_docs
echo.
echo ========================================
echo    Generating Documentation
echo ========================================
cd /d "%GEMINI_PATH%"
gemini document "%PROJECT_PATH%" --output "%WORKSPACE_PATH%\auto_documentation.md" --format markdown
echo Documentation generated. Saved to auto_documentation.md
echo.
goto menu

:custom_command
echo.
echo ========================================
echo    Custom Gemini Command
echo ========================================
set /p custom_cmd="Enter your custom Gemini command: "
cd /d "%GEMINI_PATH%"
%custom_cmd%
echo.
goto menu

:exit
echo.
echo ========================================
echo    Session Complete
echo ========================================
echo All analysis results are saved in:
echo %WORKSPACE_PATH%
echo.
echo Files created:
dir "%WORKSPACE_PATH%\*.md" /b 2>nul
echo.
echo Thank you for using Gemini CLI analysis!
pause
exit

REM ========================================
REM    Error Handling
REM ========================================
:error
echo.
echo ERROR: Something went wrong!
echo Please check:
echo 1. Gemini CLI is installed and accessible
echo 2. Project paths are correct
echo 3. You have necessary permissions
echo.
pause
exit /b 1
