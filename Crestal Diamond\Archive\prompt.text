اولا تحدث باللغة العرببية ثانية قم بطلب مساعدة من نموذج
 gemnini cli فى تحليل هذا المشروع وتشغيلة .ثانية قم بعمل مجدل خاص بك انت وgemnini cli فى الخطوات التى سوفا تقومبها من فحص وتحليل وعمل ملف ذاكرة يمكنك ان تقوم ببناء وكيل ذكر مسئول عن الذاكرة لكم 
وثالثا قم بعمل ملف يحتوى على المسارت التى سوفا تستخدمها 
نظام المساعد الذكى Collaborative_Workspace

هذا مسار المشروع الحالى C:\Users\<USER>\Crestal Diamond
وهذا مسار gemnini cli C:\Users\<USER>