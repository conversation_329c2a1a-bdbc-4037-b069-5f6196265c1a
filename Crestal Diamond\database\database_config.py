"""
🗄️ إعداد قاعدة البيانات - نظام كريستال دايموند
Database Configuration for Crestal Diamond System
"""

import mysql.connector
from mysql.connector import Error
import streamlit as st
import pandas as pd
from datetime import datetime
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """فئة إعداد قاعدة البيانات"""
    
    def __init__(self):
        self.host = "localhost"
        self.database = "crestal_diamond"
        self.user = "root"
        self.password = "2452329511"
        self.port = 3306
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                port=self.port,
                charset='utf8mb4',
                collation='utf8mb4_unicode_ci'
            )
            
            if self.connection.is_connected():
                logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
                return True
                
        except Error as e:
            logger.error(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            # محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
            return self.create_database()
        
        return False
    
    def create_database(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # الاتصال بدون تحديد قاعدة البيانات
            temp_connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                port=self.port
            )
            
            cursor = temp_connection.cursor()
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            cursor.close()
            temp_connection.close()
            
            logger.info(f"✅ تم إنشاء قاعدة البيانات {self.database}")
            
            # الآن الاتصال بقاعدة البيانات الجديدة
            return self.connect()
            
        except Error as e:
            logger.error(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("🔌 تم قطع الاتصال بقاعدة البيانات")
    
    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام SQL"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.connect()
            
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            
            if fetch:
                result = cursor.fetchall()
                cursor.close()
                return result
            else:
                self.connection.commit()
                cursor.close()
                return True
                
        except Error as e:
            logger.error(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            return None if fetch else False
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        if not self.connection or not self.connection.is_connected():
            self.connect()
        return self.connection

# إنشاء مثيل عام لقاعدة البيانات
db_config = DatabaseConfig()

def get_database_connection():
    """دالة للحصول على اتصال قاعدة البيانات"""
    return db_config.get_connection()

def execute_query(query, params=None, fetch=False):
    """دالة مساعدة لتنفيذ الاستعلامات"""
    return db_config.execute_query(query, params, fetch)

def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    if db_config.connect():
        st.success("✅ تم الاتصال بقاعدة البيانات بنجاح!")
        return True
    else:
        st.error("❌ فشل في الاتصال بقاعدة البيانات")
        return False
