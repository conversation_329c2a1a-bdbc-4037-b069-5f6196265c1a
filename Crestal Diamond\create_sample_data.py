#!/usr/bin/env python3
"""
🎲 إنشاء بيانات تجريبية شاملة - نظام كريستال دايموند
Create Comprehensive Sample Data - Crestal Diamond System
"""

import sys
import os
from datetime import datetime, date, timedelta
import random

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

try:
    from database_operations import customer_ops, invoice_ops, inventory_ops, transaction_ops
    from database_config import test_connection
    print("✅ تم تحميل وحدات قاعدة البيانات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل قاعدة البيانات: {e}")
    sys.exit(1)

def create_sample_customers():
    """إنشاء عملاء تجريبيين"""
    print("👥 إنشاء عملاء تجريبيين...")
    
    customers = [
        {
            'customer_name': 'أحمد محمد علي',
            'phone': '01234567890',
            'address': 'القاهرة، مصر الجديدة',
            'email': '<EMAIL>',
            'notes': 'عميل مميز - يفضل الذهب عيار 21'
        },
        {
            'customer_name': 'فاطمة حسن إبراهيم',
            'phone': '01098765432',
            'address': 'الجيزة، المهندسين',
            'email': '<EMAIL>',
            'notes': 'تفضل المجوهرات الكلاسيكية'
        },
        {
            'customer_name': 'محمد عبدالله أحمد',
            'phone': '01156789012',
            'address': 'الإسكندرية، سيدي جابر',
            'email': '<EMAIL>',
            'notes': 'يشتري للمناسبات الخاصة'
        },
        {
            'customer_name': 'نور الهدى محمود',
            'phone': '01087654321',
            'address': 'القاهرة، مدينة نصر',
            'email': '<EMAIL>',
            'notes': 'تحب التصاميم العصرية'
        },
        {
            'customer_name': 'علي حسام الدين',
            'phone': '01123456789',
            'address': 'الجيزة، الدقي',
            'email': '<EMAIL>',
            'notes': 'عميل تاجر - يشتري بكميات كبيرة'
        }
    ]
    
    created_count = 0
    for customer in customers:
        try:
            result = customer_ops.add_customer(customer)
            if result:
                created_count += 1
                print(f"   ✅ تم إضافة العميل: {customer['customer_name']}")
            else:
                print(f"   ⚠️ فشل في إضافة العميل: {customer['customer_name']}")
        except Exception as e:
            print(f"   ❌ خطأ في إضافة العميل {customer['customer_name']}: {e}")
    
    print(f"📊 تم إنشاء {created_count} عميل تجريبي")
    return created_count

def create_sample_inventory():
    """إنشاء مخزون تجريبي"""
    print("📦 إنشاء مخزون تجريبي...")
    
    inventory_items = [
        {
            'item_name': 'ذهب عيار 24 - سبائك مصرية',
            'category': 'ذهب خام',
            'quantity': 50,
            'unit_price_usd': 65.00,
            'unit_price_egp': 2015.00,
            'total_value_usd': 3250.00,
            'total_value_egp': 100750.00
        },
        {
            'item_name': 'ذهب عيار 21 - قطع جاهزة',
            'category': 'ذهب مشغول',
            'quantity': 25,
            'unit_price_usd': 58.00,
            'unit_price_egp': 1798.00,
            'total_value_usd': 1450.00,
            'total_value_egp': 44950.00
        },
        {
            'item_name': 'ذهب عيار 18 - خواتم وأساور',
            'category': 'مجوهرات',
            'quantity': 30,
            'unit_price_usd': 48.00,
            'unit_price_egp': 1488.00,
            'total_value_usd': 1440.00,
            'total_value_egp': 44640.00
        },
        {
            'item_name': 'أحجار كريمة - زمرد كولومبي',
            'category': 'أحجار كريمة',
            'quantity': 15,
            'unit_price_usd': 120.00,
            'unit_price_egp': 3720.00,
            'total_value_usd': 1800.00,
            'total_value_egp': 55800.00
        },
        {
            'item_name': 'أحجار كريمة - ياقوت بورمي',
            'category': 'أحجار كريمة',
            'quantity': 12,
            'unit_price_usd': 150.00,
            'unit_price_egp': 4650.00,
            'total_value_usd': 1800.00,
            'total_value_egp': 55800.00
        },
        {
            'item_name': 'ألماس طبيعي - قطع مختلفة',
            'category': 'ألماس',
            'quantity': 8,
            'unit_price_usd': 300.00,
            'unit_price_egp': 9300.00,
            'total_value_usd': 2400.00,
            'total_value_egp': 74400.00
        },
        {
            'item_name': 'لؤلؤ طبيعي - خليجي',
            'category': 'لؤلؤ',
            'quantity': 20,
            'unit_price_usd': 80.00,
            'unit_price_egp': 2480.00,
            'total_value_usd': 1600.00,
            'total_value_egp': 49600.00
        }
    ]
    
    created_count = 0
    for item in inventory_items:
        try:
            result = inventory_ops.add_inventory_item(item)
            if result:
                created_count += 1
                print(f"   ✅ تم إضافة العنصر: {item['item_name']}")
            else:
                print(f"   ⚠️ فشل في إضافة العنصر: {item['item_name']}")
        except Exception as e:
            print(f"   ❌ خطأ في إضافة العنصر {item['item_name']}: {e}")
    
    print(f"📊 تم إنشاء {created_count} عنصر مخزون تجريبي")
    return created_count

def create_sample_invoices():
    """إنشاء فواتير تجريبية"""
    print("🧾 إنشاء فواتير تجريبية...")
    
    # الحصول على قائمة العملاء
    customers = customer_ops.get_all_customers()
    if customers.empty:
        print("❌ لا توجد عملاء لإنشاء فواتير")
        return 0
    
    customer_names = customers['customer_name'].tolist()
    
    invoices = []
    for i in range(10):  # إنشاء 10 فواتير تجريبية
        customer_name = random.choice(customer_names)
        invoice_date = date.today() - timedelta(days=random.randint(1, 30))
        
        invoice = {
            'invoice_number': f'SAMPLE-{datetime.now().strftime("%Y%m%d")}-{i+1:03d}',
            'customer_name': customer_name,
            'main_product': f'خاتم ذهب عيار {random.choice([18, 21, 24])}',
            'main_product_price_usd': round(random.uniform(200, 800), 2),
            'main_product_price_egp': 0,  # سيتم حسابه
            'workshop_stones_weight': round(random.uniform(0.5, 3.0), 3),
            'workshop_stones_count': random.randint(1, 5),
            'workshop_stones_price_usd': round(random.uniform(50, 200), 2),
            'workshop_stones_price_egp': 0,
            'customer_stones_weight': round(random.uniform(0.2, 1.5), 3),
            'customer_stones_count': random.randint(0, 3),
            'customer_stones_installation_egp': round(random.uniform(100, 500), 2),
            'additional_services': 'تنظيف وتلميع',
            'additional_services_total_egp': round(random.uniform(50, 150), 2),
            'discount_percentage': round(random.uniform(0, 10), 2),
            'payment_method': random.choice(['نقدي', 'بطاقة ائتمان', 'تحويل بنكي']),
            'invoice_date': invoice_date,
            'delivery_date': invoice_date + timedelta(days=random.randint(3, 14)),
            'status': random.choice(['pending', 'completed', 'paid']),
            'notes': f'فاتورة تجريبية رقم {i+1}'
        }
        
        # حساب الإجماليات
        invoice['main_product_price_egp'] = invoice['main_product_price_usd'] * 31
        invoice['workshop_stones_price_egp'] = invoice['workshop_stones_price_usd'] * 31
        
        subtotal_usd = invoice['main_product_price_usd'] + invoice['workshop_stones_price_usd']
        subtotal_egp = invoice['main_product_price_egp'] + invoice['workshop_stones_price_egp'] + invoice['customer_stones_installation_egp'] + invoice['additional_services_total_egp']
        
        discount_amount_usd = subtotal_usd * (invoice['discount_percentage'] / 100)
        discount_amount_egp = subtotal_egp * (invoice['discount_percentage'] / 100)
        
        invoice['subtotal_usd'] = subtotal_usd
        invoice['subtotal_egp'] = subtotal_egp
        invoice['discount_amount_usd'] = discount_amount_usd
        invoice['discount_amount_egp'] = discount_amount_egp
        invoice['total_usd'] = subtotal_usd - discount_amount_usd
        invoice['total_egp'] = subtotal_egp - discount_amount_egp
        
        # مبالغ الدفع
        if invoice['status'] == 'paid':
            invoice['paid_amount_usd'] = invoice['total_usd']
            invoice['paid_amount_egp'] = invoice['total_egp']
            invoice['remaining_amount_usd'] = 0
            invoice['remaining_amount_egp'] = 0
        else:
            paid_percentage = random.uniform(0.3, 0.8)
            invoice['paid_amount_usd'] = invoice['total_usd'] * paid_percentage
            invoice['paid_amount_egp'] = invoice['total_egp'] * paid_percentage
            invoice['remaining_amount_usd'] = invoice['total_usd'] - invoice['paid_amount_usd']
            invoice['remaining_amount_egp'] = invoice['total_egp'] - invoice['paid_amount_egp']
        
        invoices.append(invoice)
    
    created_count = 0
    for invoice in invoices:
        try:
            result = invoice_ops.add_invoice(invoice)
            if result:
                created_count += 1
                print(f"   ✅ تم إضافة الفاتورة: {invoice['invoice_number']}")
            else:
                print(f"   ⚠️ فشل في إضافة الفاتورة: {invoice['invoice_number']}")
        except Exception as e:
            print(f"   ❌ خطأ في إضافة الفاتورة {invoice['invoice_number']}: {e}")
    
    print(f"📊 تم إنشاء {created_count} فاتورة تجريبية")
    return created_count

def main():
    """الدالة الرئيسية"""
    print("🎲 === بدء إنشاء البيانات التجريبية الشاملة ===")
    print("=" * 60)
    
    # اختبار الاتصال
    print("🔌 اختبار الاتصال بقاعدة البيانات...")
    if not test_connection():
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return
    
    print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
    print()
    
    # إنشاء البيانات
    customers_count = create_sample_customers()
    print()
    
    inventory_count = create_sample_inventory()
    print()
    
    invoices_count = create_sample_invoices()
    print()
    
    # تقرير نهائي
    print("=" * 60)
    print("📊 تقرير إنشاء البيانات التجريبية:")
    print(f"   👥 العملاء: {customers_count} عميل")
    print(f"   📦 المخزون: {inventory_count} عنصر")
    print(f"   🧾 الفواتير: {invoices_count} فاتورة")
    print()
    
    total_items = customers_count + inventory_count + invoices_count
    if total_items > 0:
        print("🎉 تم إنشاء البيانات التجريبية بنجاح!")
        print("💡 يمكنك الآن اختبار النظام باستخدام هذه البيانات")
    else:
        print("⚠️ لم يتم إنشاء أي بيانات تجريبية")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
