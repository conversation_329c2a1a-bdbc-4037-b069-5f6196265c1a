"""
🧪 اختبار الوكيل الذكي لقاعدة البيانات - نظام كريستال دايموند
Test Intelligent Database Agent for Crestal Diamond System
"""

from intelligent_db_agent import db_agent
import logging
from datetime import datetime

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_agent_initialization():
    """اختبار تهيئة الوكيل الذكي"""
    logger.info("🧪 اختبار تهيئة الوكيل الذكي...")
    
    try:
        # التحقق من تهيئة الوكيل
        assert db_agent is not None, "الوكيل الذكي غير مهيأ"
        assert hasattr(db_agent, 'database_schema'), "مخطط قاعدة البيانات غير موجود"
        assert hasattr(db_agent, 'validation_rules'), "قواعد التحقق غير موجودة"
        
        logger.info("✅ تم تهيئة الوكيل الذكي بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في تهيئة الوكيل الذكي: {str(e)}")
        return False

def test_connection_check():
    """اختبار فحص الاتصال"""
    logger.info("🧪 اختبار فحص الاتصال...")
    
    try:
        connection_status = db_agent._check_connection()
        
        if connection_status:
            logger.info("✅ الاتصال بقاعدة البيانات سليم")
        else:
            logger.warning("⚠️ مشكلة في الاتصال بقاعدة البيانات")
        
        return connection_status
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصال: {str(e)}")
        return False

def test_schema_integrity():
    """اختبار فحص سلامة المخطط"""
    logger.info("🧪 اختبار فحص سلامة المخطط...")
    
    try:
        schema_report = db_agent._check_schema_integrity()
        
        # التحقق من وجود الجداول الأساسية
        required_tables = ['customers', 'invoices', 'inventory', 'transactions']
        tables_exist = schema_report.get('tables_exist', {})
        
        missing_tables = [table for table in required_tables if not tables_exist.get(table, False)]
        
        if missing_tables:
            logger.warning(f"⚠️ جداول مفقودة: {missing_tables}")
            return False
        else:
            logger.info("✅ جميع الجداول الأساسية موجودة")
            return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في فحص المخطط: {str(e)}")
        return False

def test_data_validation():
    """اختبار فحص صحة البيانات"""
    logger.info("🧪 اختبار فحص صحة البيانات...")
    
    try:
        validation_report = db_agent._validate_all_data()
        
        total_valid = 0
        total_invalid = 0
        
        for table, data in validation_report.items():
            valid_count = data.get('valid', 0)
            invalid_count = data.get('invalid', 0)
            
            total_valid += valid_count
            total_invalid += invalid_count
            
            logger.info(f"📊 {table}: {valid_count} صحيح، {invalid_count} خاطئ")
        
        success_rate = (total_valid / (total_valid + total_invalid)) * 100 if (total_valid + total_invalid) > 0 else 100
        
        logger.info(f"📈 معدل صحة البيانات: {success_rate:.1f}%")
        
        return success_rate > 90  # نعتبر النجاح إذا كان أكثر من 90% من البيانات صحيحة
        
    except Exception as e:
        logger.error(f"❌ خطأ في فحص البيانات: {str(e)}")
        return False

def test_performance_check():
    """اختبار فحص الأداء"""
    logger.info("🧪 اختبار فحص الأداء...")
    
    try:
        performance_report = db_agent._check_performance()
        
        query_times = performance_report.get('query_times', {})
        total_time = sum(query_times.values())
        
        logger.info(f"⏱️ إجمالي وقت الاستعلامات: {total_time:.3f} ثانية")
        
        # نعتبر الأداء جيد إذا كان أقل من ثانيتين
        if total_time < 2.0:
            logger.info("🚀 الأداء ممتاز!")
            return True
        elif total_time < 5.0:
            logger.info("✅ الأداء جيد")
            return True
        else:
            logger.warning("⚠️ الأداء يحتاج تحسين")
            return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في فحص الأداء: {str(e)}")
        return False

def test_auto_fixes():
    """اختبار الإصلاحات التلقائية"""
    logger.info("🧪 اختبار الإصلاحات التلقائية...")
    
    try:
        fixes_applied = db_agent._apply_auto_fixes()
        
        logger.info(f"🔧 تم تطبيق {len(fixes_applied)} إصلاح تلقائي")
        
        for fix in fixes_applied:
            logger.info(f"   ✅ {fix}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تطبيق الإصلاحات: {str(e)}")
        return False

def test_comprehensive_health_check():
    """اختبار الفحص الصحي الشامل"""
    logger.info("🧪 اختبار الفحص الصحي الشامل...")
    
    try:
        health_report = db_agent.comprehensive_health_check()
        
        overall_status = health_report.get('overall_status', 'unknown')
        connection_status = health_report.get('connection_status', False)
        
        logger.info(f"🎯 الحالة العامة: {overall_status}")
        logger.info(f"🔌 حالة الاتصال: {'متصل' if connection_status else 'منقطع'}")
        
        # عرض المشاكل المكتشفة
        issues = health_report.get('issues_found', [])
        if issues:
            logger.warning(f"⚠️ تم اكتشاف {len(issues)} مشكلة:")
            for issue in issues:
                logger.warning(f"   ❌ {issue}")
        else:
            logger.info("✅ لم يتم اكتشاف أي مشاكل")
        
        # عرض الإصلاحات المطبقة
        fixes = health_report.get('auto_fixes_applied', [])
        if fixes:
            logger.info(f"🔧 تم تطبيق {len(fixes)} إصلاح:")
            for fix in fixes:
                logger.info(f"   ✅ {fix}")
        
        return overall_status in ['healthy', 'needs_attention']
        
    except Exception as e:
        logger.error(f"❌ خطأ في الفحص الشامل: {str(e)}")
        return False

def test_report_generation():
    """اختبار إنشاء التقارير"""
    logger.info("🧪 اختبار إنشاء التقارير...")
    
    try:
        # إنشاء تقرير نصي
        report = db_agent.generate_health_report()
        
        if report and len(report) > 100:  # التأكد من أن التقرير ليس فارغاً
            logger.info("✅ تم إنشاء التقرير النصي بنجاح")
            
            # حفظ التقرير في ملف
            filename = db_agent.save_report_to_file("test_report.txt")
            
            if filename:
                logger.info(f"✅ تم حفظ التقرير في: {filename}")
                return True
            else:
                logger.warning("⚠️ فشل في حفظ التقرير")
                return False
        else:
            logger.error("❌ التقرير فارغ أو قصير جداً")
            return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء التقرير: {str(e)}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    logger.info("🚀 بدء اختبار الوكيل الذكي الشامل...")
    logger.info("=" * 60)
    
    tests = [
        ("تهيئة الوكيل", test_agent_initialization),
        ("فحص الاتصال", test_connection_check),
        ("سلامة المخطط", test_schema_integrity),
        ("صحة البيانات", test_data_validation),
        ("فحص الأداء", test_performance_check),
        ("الإصلاحات التلقائية", test_auto_fixes),
        ("الفحص الشامل", test_comprehensive_health_check),
        ("إنشاء التقارير", test_report_generation)
    ]
    
    passed_tests = 0
    failed_tests = 0
    
    for test_name, test_function in tests:
        logger.info(f"\n🧪 {test_name}...")
        try:
            if test_function():
                passed_tests += 1
                logger.info(f"✅ {test_name}: نجح")
            else:
                failed_tests += 1
                logger.error(f"❌ {test_name}: فشل")
        except Exception as e:
            failed_tests += 1
            logger.error(f"❌ {test_name}: خطأ - {str(e)}")
    
    # تقرير النتائج النهائي
    logger.info("=" * 60)
    logger.info("📊 تقرير اختبار الوكيل الذكي:")
    logger.info(f"   ✅ نجح: {passed_tests}/{len(tests)} اختبار")
    logger.info(f"   ❌ فشل: {failed_tests}/{len(tests)} اختبار")
    logger.info(f"   📈 معدل النجاح: {(passed_tests/len(tests)*100):.1f}%")
    
    if failed_tests == 0:
        logger.info("🎉 جميع اختبارات الوكيل الذكي نجحت! الوكيل جاهز للعمل!")
        return True
    else:
        logger.warning(f"⚠️ {failed_tests} اختبار فشل. يرجى مراجعة الأخطاء أعلاه.")
        return False

def main():
    """تشغيل اختبارات الوكيل الذكي"""
    print("🤖 اختبار الوكيل الذكي لقاعدة البيانات - نظام كريستال دايموند")
    print("=" * 70)
    
    success = run_all_tests()
    
    if success:
        print("\n🏁 انتهت اختبارات الوكيل الذكي بنجاح!")
        print("🤖 الوكيل الذكي جاهز للمراقبة والصيانة التلقائية")
        print("🔗 يمكنك الوصول للوكيل من خلال واجهة Streamlit")
    else:
        print("\n🚨 انتهت الاختبارات مع وجود مشاكل!")
        print("🔧 يرجى إصلاح المشاكل المذكورة أعلاه")

if __name__ == "__main__":
    main()
