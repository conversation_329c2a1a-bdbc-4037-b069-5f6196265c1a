"""
🧪 اختبار قاعدة البيانات - نظام كريستال دايموند
Database Testing for Crestal Diamond System
"""

import streamlit as st
from database_config import db_config, test_connection
from create_tables import create_all_tables
from migrate_csv_to_mysql import DataMigration
from database_operations import customer_ops, invoice_ops, inventory_ops
import pandas as pd

def test_database_page():
    """صفحة اختبار قاعدة البيانات"""
    st.title("🗄️ اختبار قاعدة البيانات - نظام كريستال دايموند")
    
    # قسم اختبار الاتصال
    st.header("🔌 اختبار الاتصال")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("اختبار الاتصال", type="primary"):
            if test_connection():
                st.success("✅ تم الاتصال بقاعدة البيانات بنجاح!")
            else:
                st.error("❌ فشل في الاتصال بقاعدة البيانات")
    
    with col2:
        if st.button("إنشاء الجداول"):
            if create_all_tables():
                st.success("✅ تم إنشاء جميع الجداول بنجاح!")
            else:
                st.error("❌ فشل في إنشاء الجداول")
    
    with col3:
        if st.button("تهجير البيانات من CSV"):
            migration = DataMigration()
            if migration.run_full_migration():
                st.success("✅ تم تهجير البيانات بنجاح!")
            else:
                st.warning("⚠️ تم تهجير بعض البيانات أو لا توجد ملفات CSV")
    
    st.divider()
    
    # قسم عرض البيانات
    st.header("📊 عرض البيانات")
    
    tab1, tab2, tab3 = st.tabs(["العملاء", "الفواتير", "المخزون"])
    
    with tab1:
        st.subheader("👥 بيانات العملاء")
        try:
            customers_df = customer_ops.get_all_customers()
            if not customers_df.empty:
                st.dataframe(customers_df, use_container_width=True)
                st.info(f"📊 إجمالي العملاء: {len(customers_df)}")
            else:
                st.info("لا توجد بيانات عملاء")
        except Exception as e:
            st.error(f"خطأ في جلب بيانات العملاء: {e}")
    
    with tab2:
        st.subheader("🧾 بيانات الفواتير")
        try:
            invoices_df = invoice_ops.get_all_invoices()
            if not invoices_df.empty:
                st.dataframe(invoices_df, use_container_width=True)
                st.info(f"📊 إجمالي الفواتير: {len(invoices_df)}")
                
                # إحصائيات سريعة
                col1, col2, col3 = st.columns(3)
                with col1:
                    total_usd = invoices_df['total_usd'].sum()
                    st.metric("إجمالي المبيعات (USD)", f"${total_usd:,.2f}")
                
                with col2:
                    total_egp = invoices_df['total_egp'].sum()
                    st.metric("إجمالي المبيعات (EGP)", f"{total_egp:,.2f} ج.م")
                
                with col3:
                    pending_invoices = len(invoices_df[invoices_df['status'] == 'pending'])
                    st.metric("الفواتير المعلقة", pending_invoices)
            else:
                st.info("لا توجد بيانات فواتير")
        except Exception as e:
            st.error(f"خطأ في جلب بيانات الفواتير: {e}")
    
    with tab3:
        st.subheader("📦 بيانات المخزون")
        try:
            inventory_df = inventory_ops.get_all_inventory()
            if not inventory_df.empty:
                st.dataframe(inventory_df, use_container_width=True)
                st.info(f"📊 إجمالي عناصر المخزون: {len(inventory_df)}")
                
                # إحصائيات المخزون
                col1, col2 = st.columns(2)
                with col1:
                    total_value_usd = inventory_df['total_value_usd'].sum()
                    st.metric("قيمة المخزون (USD)", f"${total_value_usd:,.2f}")
                
                with col2:
                    total_value_egp = inventory_df['total_value_egp'].sum()
                    st.metric("قيمة المخزون (EGP)", f"{total_value_egp:,.2f} ج.م")
            else:
                st.info("لا توجد بيانات مخزون")
        except Exception as e:
            st.error(f"خطأ في جلب بيانات المخزون: {e}")
    
    st.divider()
    
    # قسم إضافة بيانات تجريبية
    st.header("🧪 إضافة بيانات تجريبية")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("إضافة عميل تجريبي"):
            test_customer = {
                'customer_name': 'عميل تجريبي',
                'phone': '01234567890',
                'address': 'عنوان تجريبي',
                'email': '<EMAIL>',
                'notes': 'عميل تجريبي للاختبار'
            }
            
            if customer_ops.add_customer(test_customer):
                st.success("✅ تم إضافة العميل التجريبي!")
            else:
                st.error("❌ فشل في إضافة العميل التجريبي")
    
    with col2:
        if st.button("إضافة عنصر مخزون تجريبي"):
            test_item = {
                'item_name': 'ذهب عيار 21 - تجريبي',
                'category': 'ذهب',
                'description': 'عنصر تجريبي للاختبار',
                'quantity': 10,
                'unit_price_usd': 50.0,
                'unit_price_egp': 1500.0,
                'total_value_usd': 500.0,
                'total_value_egp': 15000.0,
                'supplier': 'مورد تجريبي',
                'location': 'مخزن رئيسي',
                'minimum_stock': 5,
                'notes': 'عنصر تجريبي للاختبار'
            }
            
            if inventory_ops.add_inventory_item(test_item):
                st.success("✅ تم إضافة عنصر المخزون التجريبي!")
            else:
                st.error("❌ فشل في إضافة عنصر المخزون التجريبي")
    
    # معلومات قاعدة البيانات
    st.divider()
    st.header("ℹ️ معلومات قاعدة البيانات")
    
    info_col1, info_col2 = st.columns(2)
    
    with info_col1:
        st.info("""
        **إعدادات قاعدة البيانات:**
        - الخادم: localhost
        - قاعدة البيانات: crestal_diamond
        - المنفذ: 3306
        - المستخدم: root
        """)
    
    with info_col2:
        st.info("""
        **الجداول المتاحة:**
        - customers (العملاء)
        - invoices (الفواتير)
        - inventory (المخزون)
        - transactions (المعاملات)
        """)

if __name__ == "__main__":
    test_database_page()
