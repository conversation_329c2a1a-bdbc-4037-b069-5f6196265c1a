"""
⚡ تحسين الأداء والاستقرار - نظام كريستال دايموند
Performance Optimization and Stability Enhancement for Crestal Diamond System
"""

import mysql.connector
from database_config import DatabaseConfig
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def get_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    db_config = DatabaseConfig()
    return mysql.connector.connect(
        host=db_config.host,
        database=db_config.database,
        user=db_config.user,
        password=db_config.password,
        port=db_config.port
    )

def create_database_indexes():
    """إنشاء فهارس قاعدة البيانات لتحسين الأداء"""
    logger.info("📊 إنشاء فهارس قاعدة البيانات...")
    
    indexes = [
        # فهارس جدول العملاء
        "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name)",
        "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",
        
        # فهارس جدول الفواتير
        "CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_name)",
        "CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date)",
        "CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status)",
        "CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number)",
        
        # فهارس جدول المعاملات
        "CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_name)",
        "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)",
        "CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type)",
        
        # فهارس جدول المخزون
        "CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)",
        "CREATE INDEX IF NOT EXISTS idx_inventory_name ON inventory(item_name)",
        "CREATE INDEX IF NOT EXISTS idx_inventory_quantity ON inventory(quantity)"
    ]
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        created_count = 0
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                created_count += 1
                index_name = index_sql.split("idx_")[1].split(" ")[0]
                logger.info(f"✅ تم إنشاء الفهرس: idx_{index_name}")
            except Exception as e:
                logger.warning(f"⚠️ فهرس موجود مسبقاً أو خطأ: {str(e)}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logger.info(f"📊 تم إنشاء {created_count} فهرس لتحسين الأداء")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء الفهارس: {str(e)}")
        return False

def optimize_database_settings():
    """تحسين إعدادات قاعدة البيانات"""
    logger.info("⚙️ تحسين إعدادات قاعدة البيانات...")
    
    optimization_queries = [
        # تحسين إعدادات InnoDB
        "SET GLOBAL innodb_buffer_pool_size = 134217728",  # 128MB
        "SET GLOBAL innodb_log_file_size = 50331648",      # 48MB
        "SET GLOBAL innodb_flush_log_at_trx_commit = 2",
        
        # تحسين إعدادات الاستعلامات
        "SET GLOBAL query_cache_size = 16777216",          # 16MB
        "SET GLOBAL query_cache_type = ON",
        
        # تحسين إعدادات الاتصالات
        "SET GLOBAL max_connections = 100",
        "SET GLOBAL wait_timeout = 28800"
    ]
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        optimized_count = 0
        for query in optimization_queries:
            try:
                cursor.execute(query)
                optimized_count += 1
                setting_name = query.split("SET GLOBAL ")[1].split(" =")[0]
                logger.info(f"✅ تم تحسين الإعداد: {setting_name}")
            except Exception as e:
                logger.warning(f"⚠️ لا يمكن تغيير الإعداد: {str(e)}")
        
        cursor.close()
        connection.close()
        
        logger.info(f"⚙️ تم تحسين {optimized_count} إعداد")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحسين الإعدادات: {str(e)}")
        return False

def analyze_database_performance():
    """تحليل أداء قاعدة البيانات"""
    logger.info("📈 تحليل أداء قاعدة البيانات...")
    
    analysis_queries = [
        ("حجم قاعدة البيانات", """
            SELECT 
                table_schema as 'Database',
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
            FROM information_schema.tables 
            WHERE table_schema = 'crestal_diamond'
            GROUP BY table_schema
        """),
        
        ("أحجام الجداول", """
            SELECT 
                table_name as 'Table',
                ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)',
                table_rows as 'Rows'
            FROM information_schema.TABLES 
            WHERE table_schema = 'crestal_diamond'
            ORDER BY (data_length + index_length) DESC
        """),
        
        ("حالة الفهارس", """
            SELECT 
                table_name as 'Table',
                index_name as 'Index',
                cardinality as 'Cardinality'
            FROM information_schema.statistics 
            WHERE table_schema = 'crestal_diamond'
            ORDER BY table_name, index_name
        """)
    ]
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        for analysis_name, query in analysis_queries:
            logger.info(f"\n📊 {analysis_name}:")
            cursor.execute(query)
            results = cursor.fetchall()
            
            if results:
                for row in results:
                    logger.info(f"   {row}")
            else:
                logger.info("   لا توجد بيانات")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحليل الأداء: {str(e)}")
        return False

def cleanup_old_data():
    """تنظيف البيانات القديمة والمؤقتة"""
    logger.info("🧹 تنظيف البيانات القديمة...")
    
    cleanup_queries = [
        # حذف المعاملات التجريبية القديمة (أكثر من 90 يوم)
        """
        DELETE FROM transactions 
        WHERE description LIKE '%تجريبية%' 
        AND transaction_date < DATE_SUB(NOW(), INTERVAL 90 DAY)
        """,
        
        # حذف الفواتير التجريبية القديمة
        """
        DELETE FROM invoices 
        WHERE notes LIKE '%تجريبية%' 
        AND invoice_date < DATE_SUB(NOW(), INTERVAL 90 DAY)
        """,
        
        # تحديث الإحصائيات
        "ANALYZE TABLE customers, invoices, transactions, inventory"
    ]
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        cleaned_count = 0
        for query in cleanup_queries:
            try:
                cursor.execute(query)
                affected_rows = cursor.rowcount
                cleaned_count += affected_rows
                logger.info(f"✅ تم تنظيف {affected_rows} سجل")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في التنظيف: {str(e)}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logger.info(f"🧹 تم تنظيف {cleaned_count} سجل إجمالي")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تنظيف البيانات: {str(e)}")
        return False

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    logger.info("💾 إنشاء نسخة احتياطية...")
    
    import subprocess
    import os
    from datetime import datetime
    
    try:
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{backup_dir}/crestal_diamond_backup_{timestamp}.sql"
        
        # أمر النسخ الاحتياطي
        mysqldump_cmd = [
            "mysqldump",
            "-h", "localhost",
            "-u", "root",
            "-p123456",
            "--single-transaction",
            "--routines",
            "--triggers",
            "crestal_diamond"
        ]
        
        # تنفيذ النسخ الاحتياطي
        with open(backup_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(mysqldump_cmd, stdout=f, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            file_size = os.path.getsize(backup_file) / 1024 / 1024  # MB
            logger.info(f"✅ تم إنشاء النسخة الاحتياطية: {backup_file}")
            logger.info(f"📁 حجم الملف: {file_size:.2f} MB")
            return True
        else:
            logger.error(f"❌ فشل في إنشاء النسخة الاحتياطية: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return False

def main():
    """تشغيل تحسين الأداء والاستقرار"""
    logger.info("🚀 بدء تحسين الأداء والاستقرار...")
    logger.info("=" * 60)
    
    optimizations = [
        ("إنشاء الفهارس", create_database_indexes),
        ("تحسين الإعدادات", optimize_database_settings),
        ("تحليل الأداء", analyze_database_performance),
        ("تنظيف البيانات", cleanup_old_data),
        ("النسخ الاحتياطي", backup_database)
    ]
    
    successful_optimizations = 0
    
    for optimization_name, optimization_function in optimizations:
        logger.info(f"\n⚡ {optimization_name}...")
        try:
            if optimization_function():
                successful_optimizations += 1
                logger.info(f"✅ {optimization_name}: نجح")
            else:
                logger.error(f"❌ {optimization_name}: فشل")
        except Exception as e:
            logger.error(f"❌ {optimization_name}: خطأ - {str(e)}")
    
    # تقرير النتائج
    logger.info("=" * 60)
    logger.info("📊 تقرير تحسين الأداء:")
    logger.info(f"   ✅ نجح: {successful_optimizations}/{len(optimizations)} تحسين")
    logger.info(f"   📈 معدل النجاح: {(successful_optimizations/len(optimizations)*100):.1f}%")
    
    if successful_optimizations == len(optimizations):
        logger.info("🎉 تم تحسين الأداء بنجاح! النظام محسن ومستقر!")
        return True
    else:
        logger.warning(f"⚠️ {len(optimizations) - successful_optimizations} تحسين فشل.")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🏁 انتهى تحسين الأداء بنجاح!")
        print("🚀 النظام محسن ومستقر وجاهز للاستخدام")
    else:
        print("\n🚨 انتهى التحسين مع وجود مشاكل!")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
