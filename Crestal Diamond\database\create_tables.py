"""
🗄️ إنشاء جداول قاعدة البيانات - نظام كريستال دايموند
Database Tables Creation for Crestal Diamond System
"""

from database_config import db_config, execute_query
import logging

logger = logging.getLogger(__name__)

def create_customers_table():
    """إنشاء جدول العملاء"""
    query = """
    CREATE TABLE IF NOT EXISTS customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_name VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        address TEXT,
        email VARCHAR(255),
        notes TEXT,
        total_balance DECIMAL(15,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customer_name (customer_name),
        INDEX idx_phone (phone)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
    
    if execute_query(query):
        logger.info("✅ تم إنشاء جدول العملاء")
        return True
    return False

def create_invoices_table():
    """إنشاء جدول الفواتير"""
    query = """
    CREATE TABLE IF NOT EXISTS invoices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        customer_id INT,
        customer_name VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        
        -- تفاصيل المنتج الرئيسي
        main_product VARCHAR(255),
        main_product_price_usd DECIMAL(15,2),
        main_product_price_egp DECIMAL(15,2),
        
        -- تفاصيل الأحجار
        workshop_stones_weight DECIMAL(10,3),
        workshop_stones_count INT,
        workshop_stones_price_usd DECIMAL(15,2),
        workshop_stones_price_egp DECIMAL(15,2),
        
        customer_stones_weight DECIMAL(10,3),
        customer_stones_count INT,
        customer_stones_installation_egp DECIMAL(15,2),
        
        -- الخدمات الإضافية
        additional_services TEXT,
        additional_services_total_egp DECIMAL(15,2),
        
        -- المجاميع
        subtotal_usd DECIMAL(15,2),
        subtotal_egp DECIMAL(15,2),
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        discount_amount_usd DECIMAL(15,2) DEFAULT 0.00,
        discount_amount_egp DECIMAL(15,2) DEFAULT 0.00,
        total_usd DECIMAL(15,2),
        total_egp DECIMAL(15,2),
        
        -- معلومات الدفع
        payment_method VARCHAR(100),
        paid_amount_usd DECIMAL(15,2) DEFAULT 0.00,
        paid_amount_egp DECIMAL(15,2) DEFAULT 0.00,
        remaining_amount_usd DECIMAL(15,2) DEFAULT 0.00,
        remaining_amount_egp DECIMAL(15,2) DEFAULT 0.00,
        
        -- معلومات إضافية
        notes TEXT,
        invoice_date DATE,
        delivery_date DATE,
        status VARCHAR(50) DEFAULT 'pending',
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        INDEX idx_invoice_number (invoice_number),
        INDEX idx_customer_id (customer_id),
        INDEX idx_invoice_date (invoice_date),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
    
    if execute_query(query):
        logger.info("✅ تم إنشاء جدول الفواتير")
        return True
    return False

def create_inventory_table():
    """إنشاء جدول المخزون"""
    query = """
    CREATE TABLE IF NOT EXISTS inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        item_name VARCHAR(255) NOT NULL,
        category VARCHAR(100),
        description TEXT,
        quantity INT DEFAULT 0,
        unit_price_usd DECIMAL(15,2),
        unit_price_egp DECIMAL(15,2),
        total_value_usd DECIMAL(15,2),
        total_value_egp DECIMAL(15,2),
        supplier VARCHAR(255),
        location VARCHAR(255),
        minimum_stock INT DEFAULT 0,
        notes TEXT,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_item_name (item_name),
        INDEX idx_category (category),
        INDEX idx_quantity (quantity)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
    
    if execute_query(query):
        logger.info("✅ تم إنشاء جدول المخزون")
        return True
    return False

def create_transactions_table():
    """إنشاء جدول المعاملات المالية"""
    query = """
    CREATE TABLE IF NOT EXISTS transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        invoice_id INT,
        transaction_type VARCHAR(50) NOT NULL, -- 'payment', 'refund', 'adjustment'
        amount_usd DECIMAL(15,2) DEFAULT 0.00,
        amount_egp DECIMAL(15,2) DEFAULT 0.00,
        payment_method VARCHAR(100),
        transaction_date DATE,
        description TEXT,
        reference_number VARCHAR(100),
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
        INDEX idx_customer_id (customer_id),
        INDEX idx_invoice_id (invoice_id),
        INDEX idx_transaction_date (transaction_date),
        INDEX idx_transaction_type (transaction_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
    
    if execute_query(query):
        logger.info("✅ تم إنشاء جدول المعاملات")
        return True
    return False

def create_all_tables():
    """إنشاء جميع الجداول"""
    logger.info("🚀 بدء إنشاء جداول قاعدة البيانات...")
    
    if not db_config.connect():
        logger.error("❌ فشل في الاتصال بقاعدة البيانات")
        return False
    
    tables_created = 0
    
    if create_customers_table():
        tables_created += 1
    
    if create_invoices_table():
        tables_created += 1
    
    if create_inventory_table():
        tables_created += 1
    
    if create_transactions_table():
        tables_created += 1
    
    logger.info(f"✅ تم إنشاء {tables_created} جداول بنجاح")
    return tables_created == 4

if __name__ == "__main__":
    create_all_tables()
