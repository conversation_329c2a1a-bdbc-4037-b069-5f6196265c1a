#!/usr/bin/env python3
"""
🔧 تحديث هيكل قاعدة البيانات - نظام كريستال دايموند
Update Database Structure - Crestal Diamond System
"""

import sys
import os
from datetime import datetime

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.dirname(__file__))

try:
    from database_config import execute_query, test_connection
    print("✅ تم تحميل وحدات قاعدة البيانات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل قاعدة البيانات: {e}")
    sys.exit(1)

def update_invoices_table():
    """تحديث جدول الفواتير لإضافة الحقول الجديدة"""
    
    print("🔧 تحديث جدول الفواتير...")
    
    # قائمة التحديثات المطلوبة
    updates = [
        {
            'name': 'إضافة عيار الذهب',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS gold_carat ENUM('18', '21', '24') DEFAULT '18'
            """
        },
        {
            'name': 'إضافة مصدر الأحجار',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS stone_source ENUM('طرفكم', 'طرفنا', 'مختلط') DEFAULT 'طرفكم'
            """
        },
        {
            'name': 'إضافة خدمة البانيو',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS banio_service BOOLEAN DEFAULT FALSE
            """
        },
        {
            'name': 'إضافة سعر البانيو',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS banio_price_egp DECIMAL(10,2) DEFAULT 0
            """
        },
        {
            'name': 'إضافة خدمة الدمغة',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS stamp_service BOOLEAN DEFAULT FALSE
            """
        },
        {
            'name': 'إضافة سعر الدمغة',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS stamp_price_egp DECIMAL(10,2) DEFAULT 20
            """
        },
        {
            'name': 'إضافة معدل المصنعية الثابت',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS workmanship_rate_usd DECIMAL(5,2) DEFAULT 10.00
            """
        },
        {
            'name': 'إضافة معدل تركيب الأحجار',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS stone_setting_rate_egp DECIMAL(5,2) DEFAULT 5.00
            """
        },
        {
            'name': 'إضافة ملاحظات الخدمات',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS service_notes TEXT
            """
        },
        {
            'name': 'إضافة حالة الطلب',
            'query': """
            ALTER TABLE invoices 
            ADD COLUMN IF NOT EXISTS order_status ENUM('جديد', 'قيد التنفيذ', 'جاهز', 'مسلم') DEFAULT 'جديد'
            """
        }
    ]
    
    success_count = 0
    for update in updates:
        try:
            result = execute_query(update['query'])
            if result:
                print(f"   ✅ {update['name']}")
                success_count += 1
            else:
                print(f"   ⚠️ {update['name']} - قد يكون موجود مسبقاً")
        except Exception as e:
            print(f"   ❌ {update['name']}: {e}")
    
    print(f"\n📊 تم تطبيق {success_count}/{len(updates)} تحديث بنجاح")
    return success_count

def create_new_invoice_template_table():
    """إنشاء جدول نموذج الفاتورة الجديد"""
    
    print("\n🆕 إنشاء جدول نموذج الفاتورة المحدث...")
    
    query = """
    CREATE TABLE IF NOT EXISTS invoice_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        template_name VARCHAR(100) NOT NULL,
        
        -- معدلات ثابتة
        workmanship_rate_usd DECIMAL(5,2) DEFAULT 10.00,
        stone_setting_rate_egp DECIMAL(5,2) DEFAULT 5.00,
        stamp_base_price_egp DECIMAL(5,2) DEFAULT 20.00,
        
        -- خدمات افتراضية
        default_banio_service BOOLEAN DEFAULT TRUE,
        default_stamp_service BOOLEAN DEFAULT TRUE,
        
        -- ملاحظات
        template_notes TEXT,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
    """
    
    try:
        result = execute_query(query)
        if result:
            print("   ✅ تم إنشاء جدول نماذج الفواتير")
            
            # إدراج نموذج افتراضي
            insert_query = """
            INSERT IGNORE INTO invoice_templates 
            (template_name, workmanship_rate_usd, stone_setting_rate_egp, stamp_base_price_egp, template_notes)
            VALUES 
            ('النموذج الافتراضي', 10.00, 5.00, 20.00, 'النموذج المطابق لملفات الإكسل الحالية')
            """
            
            execute_query(insert_query)
            print("   ✅ تم إدراج النموذج الافتراضي")
            return True
        else:
            print("   ❌ فشل في إنشاء جدول نماذج الفواتير")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء جدول نماذج الفواتير: {e}")
        return False

def verify_updates():
    """التحقق من التحديثات المطبقة"""
    
    print("\n🔍 التحقق من التحديثات المطبقة...")
    
    # فحص هيكل جدول الفواتير
    query = "DESCRIBE invoices"
    result = execute_query(query, fetch=True)
    
    if result:
        print("📊 هيكل جدول الفواتير المحدث:")
        new_columns = []
        for row in result:
            column_name = row['Field']
            column_type = row['Type']
            default_value = row['Default']
            
            # التحقق من الأعمدة الجديدة
            if column_name in ['gold_carat', 'stone_source', 'banio_service', 'banio_price_egp', 
                              'stamp_service', 'stamp_price_egp', 'workmanship_rate_usd', 
                              'stone_setting_rate_egp', 'service_notes', 'order_status']:
                new_columns.append(column_name)
                print(f"   ✅ {column_name}: {column_type} (افتراضي: {default_value})")
        
        print(f"\n📈 تم إضافة {len(new_columns)} عمود جديد")
        return len(new_columns)
    else:
        print("❌ فشل في فحص هيكل الجدول")
        return 0

def main():
    """الدالة الرئيسية"""
    
    print("🔧 === بدء تحديث هيكل قاعدة البيانات ===")
    print("=" * 60)
    
    # اختبار الاتصال
    print("🔌 اختبار الاتصال بقاعدة البيانات...")
    if not test_connection():
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return
    
    print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
    
    # تطبيق التحديثات
    updates_applied = update_invoices_table()
    
    # إنشاء جدول النماذج
    template_created = create_new_invoice_template_table()
    
    # التحقق من التحديثات
    columns_added = verify_updates()
    
    # تقرير نهائي
    print("\n" + "=" * 60)
    print("📊 تقرير تحديث قاعدة البيانات:")
    print(f"   🔧 التحديثات المطبقة: {updates_applied}")
    print(f"   🆕 جدول النماذج: {'✅ تم إنشاؤه' if template_created else '❌ فشل'}")
    print(f"   📈 الأعمدة الجديدة: {columns_added}")
    
    if updates_applied > 0 and template_created and columns_added > 0:
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("💡 يمكنك الآن استخدام الحقول الجديدة في النظام")
    else:
        print("\n⚠️ تم تطبيق التحديثات جزئياً")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
