import streamlit as st
import pandas as pd
import os
import sys
from datetime import datetime, date

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

# استيراد وحدات قاعدة البيانات
try:
    from database.database_operations import inventory_ops
    from database.database_config import test_connection
    DATABASE_AVAILABLE = True
except ImportError as e:
    st.error(f"خطأ في تحميل قاعدة البيانات: {e}")
    DATABASE_AVAILABLE = False

st.set_page_config(layout="wide", page_title="مخزون الذهب - Crestal Diamond")
st.title("🪙 إدارة مخزون الذهب - MySQL")

# --- دالة لحفظ حركة الذهب في قاعدة البيانات ---
def save_gold_transaction(date, desc, carat, weight_change):
    """حفظ معاملة ذهب في قاعدة البيانات"""
    if DATABASE_AVAILABLE:
        try:
            # إضافة عنصر جديد للمخزون
            inventory_data = {
                'item_name': f'ذهب عيار {carat} - {desc}',
                'category': 'ذهب',
                'quantity': 1,
                'unit_price_usd': 0,  # يمكن تحديثه لاحقاً
                'unit_price_egp': 0,
                'total_value_usd': 0,
                'total_value_egp': 0
            }
            result = inventory_ops.add_inventory_item(inventory_data)
            return result
        except Exception as e:
            st.error(f"خطأ في حفظ المعاملة: {e}")
            return False
    else:
        st.error("قاعدة البيانات غير متاحة")
        return False

# --- نموذج لإضافة مخزون جديد ---
with st.expander("➕ إضافة رصيد ذهب جديد"):
    with st.form("gold_form", clear_on_submit=True):
        g_date = st.date_input("تاريخ الإضافة")
        g_desc = st.text_input("البيان", placeholder="مثال: شراء سبيكة جديدة")
        
        c1, c2 = st.columns(2)
        with c1:
            g_carat = st.selectbox("العيار", [24, 21, 18, 14, 9])
        with c2:
            g_weight = st.number_input("الوزن بالجرام", min_value=0.0, format="%.2f")
            
        submitted = st.form_submit_button("حفظ الإضافة")
        if submitted:
            save_gold_transaction(str(g_date), g_desc, g_carat, g_weight)
            st.success(f"تمت إضافة {g_weight} جرام من عيار {g_carat} إلى المخزون.")

st.write("---")

# --- عرض الأرصدة الحالية ---
st.header("📊 الأرصدة الحالية للمخزون")

if DATABASE_AVAILABLE:
    try:
        # جلب بيانات المخزون من قاعدة البيانات
        inventory_data = inventory_ops.get_all_inventory()

        if inventory_data.empty:
            st.info("لا يوجد مخزون مسجل بعد.")
        else:
            # عرض بيانات المخزون
            st.dataframe(inventory_data, use_container_width=True)

            # إحصائيات سريعة
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("إجمالي العناصر", len(inventory_data))
            with col2:
                total_value_usd = inventory_data['total_value_usd'].sum()
                st.metric("القيمة الإجمالية (USD)", f"${total_value_usd:,.2f}")
            with col3:
                total_quantity = inventory_data['quantity'].sum()
                st.metric("إجمالي الكمية", f"{total_quantity:,}")

    except Exception as e:
        st.error(f"خطأ في جلب بيانات المخزون: {e}")
        st.info("لا يوجد مخزون مسجل بعد.")
else:
    st.error("قاعدة البيانات غير متاحة")
    st.info("يرجى التحقق من إعدادات قاعدة البيانات")