import streamlit as st
import pandas as pd
import os
import sys
from datetime import datetime, date

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

# استيراد وحدات قاعدة البيانات
try:
    from database.database_operations import inventory_ops
    from database.database_config import test_connection
    DATABASE_AVAILABLE = True
except ImportError as e:
    st.error(f"خطأ في تحميل قاعدة البيانات: {e}")
    DATABASE_AVAILABLE = False

st.set_page_config(layout="wide", page_title="مخزون الذهب - Crestal Diamond")
st.title("🪙 إدارة مخزون الذهب - MySQL")

# --- دالة لحفظ حركة الذهب ---
def save_gold_transaction(date, desc, carat, weight_change):
    columns = ["date", "description", "carat", "weight_change"]
    new_data = pd.DataFrame([[date, desc, carat, weight_change]], columns=columns)
    
    if not os.path.isfile(GOLD_FILE_PATH):
        new_data.to_csv(GOLD_FILE_PATH, index=False, encoding='utf-8-sig')
    else:
        new_data.to_csv(GOLD_FILE_PATH, mode='a', header=False, index=False, encoding='utf-8-sig')

# --- نموذج لإضافة مخزون جديد ---
with st.expander("➕ إضافة رصيد ذهب جديد"):
    with st.form("gold_form", clear_on_submit=True):
        g_date = st.date_input("تاريخ الإضافة")
        g_desc = st.text_input("البيان", placeholder="مثال: شراء سبيكة جديدة")
        
        c1, c2 = st.columns(2)
        with c1:
            g_carat = st.selectbox("العيار", [24, 21, 18, 14, 9])
        with c2:
            g_weight = st.number_input("الوزن بالجرام", min_value=0.0, format="%.2f")
            
        submitted = st.form_submit_button("حفظ الإضافة")
        if submitted:
            save_gold_transaction(str(g_date), g_desc, g_carat, g_weight)
            st.success(f"تمت إضافة {g_weight} جرام من عيار {g_carat} إلى المخزون.")

st.write("---")

# --- عرض الأرصدة الحالية ---
st.header("📊 الأرصدة الحالية للمخزون")

if not os.path.isfile(GOLD_FILE_PATH):
    st.info("لا يوجد مخزون مسجل بعد.")
else:
    df = pd.read_csv(GOLD_FILE_PATH)
    
    # حساب الرصيد لكل عيار
    balance = df.groupby('carat')['weight_change'].sum().reset_index()
    
    cols = st.columns(len(balance))
    for i, row in balance.iterrows():
        cols[i].metric(f"رصيد عيار {row['carat']}", f"{row['weight_change']:.2f} جرام")
        
    st.write("---")
    
    # عرض سجل الحركات
    st.subheader("📜 سجل حركات الذهب")
    st.dataframe(df.sort_values(by='date', ascending=False), use_container_width=True)