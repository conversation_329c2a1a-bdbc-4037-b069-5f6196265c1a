"""
📊 إضافة بيانات تجريبية شاملة - نظام كريستال دايموند
Add Comprehensive Sample Data for Crestal Diamond System
"""

from database_operations import customer_ops, invoice_ops, inventory_ops, transaction_ops
from datetime import datetime, date, timedelta
import random
import logging

logger = logging.getLogger(__name__)

def add_sample_customers():
    """إضافة عملاء تجريبيين"""
    customers = [
        {
            'customer_name': 'أحمد محمد علي',
            'phone': '01012345678',
            'address': 'القاهرة - مصر الجديدة',
            'email': '<EMAIL>',
            'notes': 'عميل VIP - يفضل الذهب عيار 21'
        },
        {
            'customer_name': 'فاطمة حسن',
            'phone': '01123456789',
            'address': 'الجيزة - المهندسين',
            'email': '<EMAIL>',
            'notes': 'تفضل المجوهرات الكلاسيكية'
        },
        {
            'customer_name': 'محمد عبدالله',
            'phone': '01234567890',
            'address': 'الإسكندرية - سيدي جابر',
            'email': '<EMAIL>',
            'notes': 'يشتري للمناسبات الخاصة'
        },
        {
            'customer_name': 'سارة أحمد',
            'phone': '01098765432',
            'address': 'القاهرة - الزمالك',
            'email': '<EMAIL>',
            'notes': 'تحب التصاميم العصرية'
        },
        {
            'customer_name': 'علي حسام',
            'phone': '01187654321',
            'address': 'الجيزة - الدقي',
            'email': '<EMAIL>',
            'notes': 'عميل منتظم - يشتري شهرياً'
        }
    ]
    
    added_count = 0
    for customer in customers:
        if customer_ops.add_customer(customer):
            added_count += 1
            logger.info(f"✅ تم إضافة العميل: {customer['customer_name']}")
        else:
            logger.error(f"❌ فشل في إضافة العميل: {customer['customer_name']}")
    
    logger.info(f"✅ تم إضافة {added_count} عميل جديد")
    return added_count

def add_sample_inventory():
    """إضافة عناصر مخزون تجريبية"""
    inventory_items = [
        {
            'item_name': 'ذهب عيار 24 - سبائك',
            'category': 'ذهب خام',
            'description': 'سبائك ذهب عيار 24 للتصنيع',
            'quantity': 50,
            'unit_price_usd': 65.0,
            'unit_price_egp': 2000.0,
            'total_value_usd': 3250.0,
            'total_value_egp': 100000.0,
            'supplier': 'شركة الذهب المصرية',
            'location': 'خزنة رئيسية',
            'minimum_stock': 10,
            'notes': 'للتصنيع والتشكيل'
        },
        {
            'item_name': 'ذهب عيار 21 - مشغول',
            'category': 'ذهب مشغول',
            'description': 'قطع ذهب عيار 21 جاهزة للبيع',
            'quantity': 25,
            'unit_price_usd': 58.0,
            'unit_price_egp': 1800.0,
            'total_value_usd': 1450.0,
            'total_value_egp': 45000.0,
            'supplier': 'ورشة كريستال',
            'location': 'صالة العرض',
            'minimum_stock': 5,
            'notes': 'قطع جاهزة للعملاء'
        },
        {
            'item_name': 'ذهب عيار 18 - خواتم',
            'category': 'خواتم',
            'description': 'خواتم ذهب عيار 18 متنوعة',
            'quantity': 15,
            'unit_price_usd': 45.0,
            'unit_price_egp': 1400.0,
            'total_value_usd': 675.0,
            'total_value_egp': 21000.0,
            'supplier': 'ورشة الفن الذهبي',
            'location': 'صالة العرض',
            'minimum_stock': 3,
            'notes': 'تشكيلة متنوعة من الخواتم'
        },
        {
            'item_name': 'أحجار كريمة - زمرد',
            'category': 'أحجار كريمة',
            'description': 'أحجار زمرد طبيعية عالية الجودة',
            'quantity': 8,
            'unit_price_usd': 200.0,
            'unit_price_egp': 6200.0,
            'total_value_usd': 1600.0,
            'total_value_egp': 49600.0,
            'supplier': 'تجار الأحجار الكريمة',
            'location': 'خزنة خاصة',
            'minimum_stock': 2,
            'notes': 'أحجار عالية الجودة للقطع المميزة'
        },
        {
            'item_name': 'أحجار كريمة - ياقوت',
            'category': 'أحجار كريمة',
            'description': 'أحجار ياقوت أحمر وأزرق',
            'quantity': 6,
            'unit_price_usd': 300.0,
            'unit_price_egp': 9300.0,
            'total_value_usd': 1800.0,
            'total_value_egp': 55800.0,
            'supplier': 'تجار الأحجار الكريمة',
            'location': 'خزنة خاصة',
            'minimum_stock': 1,
            'notes': 'أحجار نادرة للقطع الفاخرة'
        }
    ]
    
    added_count = 0
    for item in inventory_items:
        if inventory_ops.add_inventory_item(item):
            added_count += 1
            logger.info(f"✅ تم إضافة عنصر المخزون: {item['item_name']}")
        else:
            logger.error(f"❌ فشل في إضافة عنصر المخزون: {item['item_name']}")
    
    logger.info(f"✅ تم إضافة {added_count} عنصر مخزون جديد")
    return added_count

def add_sample_invoices():
    """إضافة فواتير تجريبية"""
    base_date = date.today() - timedelta(days=30)
    
    invoices = []
    customers = ['أحمد محمد علي', 'فاطمة حسن', 'محمد عبدالله', 'سارة أحمد', 'علي حسام']
    products = ['خاتم ذهب عيار 21', 'سلسلة ذهب', 'أقراط ذهب', 'سوار ذهب', 'دبلة زواج']
    
    for i in range(10):
        invoice_date = base_date + timedelta(days=random.randint(0, 30))
        customer = random.choice(customers)
        product = random.choice(products)
        
        # أسعار عشوائية واقعية
        usd_price = random.randint(100, 800)
        egp_price = usd_price * random.randint(30, 32)
        
        invoice = {
            'invoice_number': f'INV-SAMPLE-{i+1:03d}',
            'customer_name': customer,
            'phone': f'010{random.randint(10000000, 99999999)}',
            'address': 'عنوان العميل',
            'main_product': product,
            'main_product_price_usd': usd_price,
            'main_product_price_egp': egp_price,
            'workshop_stones_weight': random.uniform(0.5, 3.0),
            'workshop_stones_count': random.randint(1, 5),
            'workshop_stones_price_usd': random.randint(20, 100),
            'workshop_stones_price_egp': random.randint(600, 3000),
            'subtotal_usd': usd_price + random.randint(20, 100),
            'subtotal_egp': egp_price + random.randint(600, 3000),
            'discount_percentage': random.choice([0, 5, 10]),
            'total_usd': 0,  # سيتم حسابه
            'total_egp': 0,  # سيتم حسابه
            'payment_method': random.choice(['نقدي', 'بطاقة ائتمان', 'تحويل بنكي']),
            'paid_amount_usd': 0,  # سيتم حسابه
            'paid_amount_egp': 0,  # سيتم حسابه
            'notes': f'فاتورة تجريبية رقم {i+1}',
            'invoice_date': invoice_date,
            'status': random.choice(['completed', 'pending', 'paid'])
        }
        
        # حساب المجاميع
        discount_amount_usd = invoice['subtotal_usd'] * invoice['discount_percentage'] / 100
        discount_amount_egp = invoice['subtotal_egp'] * invoice['discount_percentage'] / 100
        
        invoice['total_usd'] = invoice['subtotal_usd'] - discount_amount_usd
        invoice['total_egp'] = invoice['subtotal_egp'] - discount_amount_egp
        
        # حساب المدفوع (نسبة عشوائية)
        payment_ratio = random.uniform(0.5, 1.0)
        invoice['paid_amount_usd'] = invoice['total_usd'] * payment_ratio
        invoice['paid_amount_egp'] = invoice['total_egp'] * payment_ratio
        
        invoice['remaining_amount_usd'] = invoice['total_usd'] - invoice['paid_amount_usd']
        invoice['remaining_amount_egp'] = invoice['total_egp'] - invoice['paid_amount_egp']
        
        invoices.append(invoice)
    
    added_count = 0
    for invoice in invoices:
        if invoice_ops.add_invoice(invoice):
            added_count += 1
            logger.info(f"✅ تم إضافة الفاتورة: {invoice['invoice_number']}")
        else:
            logger.error(f"❌ فشل في إضافة الفاتورة: {invoice['invoice_number']}")
    
    logger.info(f"✅ تم إضافة {added_count} فاتورة جديدة")
    return added_count

def run_sample_data_creation():
    """تشغيل إضافة البيانات التجريبية"""
    logger.info("🚀 بدء إضافة البيانات التجريبية الشاملة...")
    
    results = {}
    
    # إضافة العملاء
    results['customers'] = add_sample_customers()
    
    # إضافة المخزون
    results['inventory'] = add_sample_inventory()
    
    # إضافة الفواتير
    results['invoices'] = add_sample_invoices()
    
    # تقرير النتائج
    total_added = sum(results.values())
    
    logger.info(f"📊 تقرير إضافة البيانات التجريبية:")
    logger.info(f"   - العملاء: {results['customers']}")
    logger.info(f"   - المخزون: {results['inventory']}")
    logger.info(f"   - الفواتير: {results['invoices']}")
    logger.info(f"   - الإجمالي: {total_added} عنصر")
    
    if total_added > 0:
        logger.info("🎉 تم إضافة البيانات التجريبية بنجاح!")
        return True
    else:
        logger.warning("⚠️ لم يتم إضافة أي بيانات تجريبية")
        return False

if __name__ == "__main__":
    # إعداد السجلات
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # تشغيل إضافة البيانات
    success = run_sample_data_creation()
    
    if success:
        print("\n🏁 انتهت إضافة البيانات التجريبية بنجاح!")
    else:
        print("\n🚨 انتهت إضافة البيانات مع وجود مشاكل!")
