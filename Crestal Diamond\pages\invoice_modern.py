"""
📝 صفحة إنشاء الفواتير الحديثة - نظام كريستال دايموند
Modern Invoice Creation Page - Crestal Diamond System
"""

import streamlit as st
from streamlit_shadcn_ui import ui
import sys
import os
from datetime import datetime, date
import pandas as pd

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

try:
    from database.database_operations import customer_ops, invoice_ops
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

# إعداد الصفحة
st.set_page_config(
    page_title="📝 إنشاء فاتورة - Crestal Diamond",
    page_icon="📝",
    layout="wide"
)

# CSS مخصص للفاتورة
st.markdown("""
<style>
    .invoice-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .form-section {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        border-left: 4px solid #667eea;
    }
    
    .calculation-box {
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        border-radius: 12px;
        border: 2px solid #dee2e6;
        margin: 1rem 0;
    }
    
    .total-box {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin: 1rem 0;
    }
    
    .currency-toggle {
        display: flex;
        justify-content: center;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def invoice_form():
    """نموذج إنشاء الفاتورة الحديث"""
    
    # العنوان
    st.markdown("""
    <div class="invoice-header">
        <h1>📝 إنشاء فاتورة جديدة</h1>
        <p>نظام فواتير احترافي للمجوهرات والذهب</p>
    </div>
    """, unsafe_allow_html=True)
    
    # نموذج الفاتورة
    with st.form("invoice_form", clear_on_submit=False):
        
        # قسم معلومات العميل
        st.markdown('<div class="form-section">', unsafe_allow_html=True)
        st.subheader("👤 معلومات العميل")
        
        col1, col2 = st.columns(2)
        
        with col1:
            customer_name = st.text_input("اسم العميل *", placeholder="أدخل اسم العميل")
            phone = st.text_input("رقم الهاتف", placeholder="01xxxxxxxxx")
        
        with col2:
            address = st.text_area("العنوان", placeholder="عنوان العميل", height=100)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # قسم تفاصيل المنتج
        st.markdown('<div class="form-section">', unsafe_allow_html=True)
        st.subheader("💎 تفاصيل المنتج الرئيسي")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            main_product = st.text_input("اسم المنتج *", placeholder="مثال: خاتم ذهب عيار 21")
        
        with col2:
            main_price_usd = st.number_input("السعر (USD)", min_value=0.0, step=0.01, format="%.2f")
        
        with col3:
            main_price_egp = st.number_input("السعر (EGP)", min_value=0.0, step=0.01, format="%.2f")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # قسم أحجار الورشة
        st.markdown('<div class="form-section">', unsafe_allow_html=True)
        st.subheader("💍 أحجار الورشة")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            workshop_stones_weight = st.number_input("الوزن (جرام)", min_value=0.0, step=0.1, format="%.3f", key="ws_weight")
        
        with col2:
            workshop_stones_count = st.number_input("العدد", min_value=0, step=1, key="ws_count")
        
        with col3:
            workshop_stones_price_usd = st.number_input("السعر (USD)", min_value=0.0, step=0.01, format="%.2f", key="ws_usd")
        
        with col4:
            workshop_stones_price_egp = st.number_input("السعر (EGP)", min_value=0.0, step=0.01, format="%.2f", key="ws_egp")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # قسم أحجار العميل
        st.markdown('<div class="form-section">', unsafe_allow_html=True)
        st.subheader("💎 أحجار العميل")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            customer_stones_weight = st.number_input("الوزن (جرام)", min_value=0.0, step=0.1, format="%.3f", key="cs_weight")
        
        with col2:
            customer_stones_count = st.number_input("العدد", min_value=0, step=1, key="cs_count")
        
        with col3:
            customer_stones_installation_egp = st.number_input("تكلفة التركيب (EGP)", min_value=0.0, step=0.01, format="%.2f")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # قسم الخدمات الإضافية
        st.markdown('<div class="form-section">', unsafe_allow_html=True)
        st.subheader("🛠️ الخدمات الإضافية")
        
        additional_services = st.text_area("وصف الخدمات", placeholder="مثال: تنظيف، تلميع، إصلاح...")
        additional_services_total_egp = st.number_input("إجمالي الخدمات (EGP)", min_value=0.0, step=0.01, format="%.2f")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # حساب المجاميع
        subtotal_usd = main_price_usd + workshop_stones_price_usd
        subtotal_egp = main_price_egp + workshop_stones_price_egp + customer_stones_installation_egp + additional_services_total_egp
        
        # قسم الخصم والمجموع
        st.markdown('<div class="form-section">', unsafe_allow_html=True)
        st.subheader("💰 الحسابات النهائية")
        
        col1, col2 = st.columns(2)
        
        with col1:
            discount_percentage = st.slider("نسبة الخصم (%)", min_value=0.0, max_value=50.0, step=0.5, format="%.1f")
        
        with col2:
            payment_method = st.selectbox("طريقة الدفع", ["نقدي", "بطاقة ائتمان", "تحويل بنكي", "شيك"])
        
        # حساب الخصم والمجموع النهائي
        discount_amount_usd = subtotal_usd * discount_percentage / 100
        discount_amount_egp = subtotal_egp * discount_percentage / 100
        
        total_usd = subtotal_usd - discount_amount_usd
        total_egp = subtotal_egp - discount_amount_egp
        
        # عرض الحسابات
        st.markdown('<div class="calculation-box">', unsafe_allow_html=True)
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**💵 بالدولار الأمريكي (USD)**")
            st.write(f"المجموع الفرعي: ${subtotal_usd:.2f}")
            st.write(f"الخصم ({discount_percentage}%): ${discount_amount_usd:.2f}")
            st.markdown(f"**المجموع النهائي: ${total_usd:.2f}**")
        
        with col2:
            st.markdown("**💰 بالجنيه المصري (EGP)**")
            st.write(f"المجموع الفرعي: {subtotal_egp:.2f} ج.م")
            st.write(f"الخصم ({discount_percentage}%): {discount_amount_egp:.2f} ج.م")
            st.markdown(f"**المجموع النهائي: {total_egp:.2f} ج.م**")
        
        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
        
        # قسم الدفع
        st.markdown('<div class="form-section">', unsafe_allow_html=True)
        st.subheader("💳 تفاصيل الدفع")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            paid_amount_usd = st.number_input("المدفوع (USD)", min_value=0.0, max_value=float(total_usd), step=0.01, format="%.2f")
        
        with col2:
            paid_amount_egp = st.number_input("المدفوع (EGP)", min_value=0.0, max_value=float(total_egp), step=0.01, format="%.2f")
        
        with col3:
            remaining_usd = total_usd - paid_amount_usd
            st.metric("المتبقي (USD)", f"${remaining_usd:.2f}")
        
        with col4:
            remaining_egp = total_egp - paid_amount_egp
            st.metric("المتبقي (EGP)", f"{remaining_egp:.2f} ج.م")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # ملاحظات إضافية
        notes = st.text_area("ملاحظات إضافية", placeholder="أي ملاحظات خاصة بالفاتورة...")
        
        # أزرار الإجراءات
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col1:
            submit_button = st.form_submit_button("💾 حفظ الفاتورة", type="primary", use_container_width=True)
        
        with col2:
            preview_button = st.form_submit_button("👁️ معاينة", use_container_width=True)
        
        with col3:
            clear_button = st.form_submit_button("🗑️ مسح الكل", use_container_width=True)
        
        # معالجة الإجراءات
        if submit_button:
            if customer_name and main_product:
                # إنشاء رقم فاتورة فريد
                invoice_number = f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                # تحضير بيانات الفاتورة
                invoice_data = {
                    'invoice_number': invoice_number,
                    'customer_name': customer_name,
                    'phone': phone,
                    'address': address,
                    'main_product': main_product,
                    'main_product_price_usd': main_price_usd,
                    'main_product_price_egp': main_price_egp,
                    'workshop_stones_weight': workshop_stones_weight,
                    'workshop_stones_count': workshop_stones_count,
                    'workshop_stones_price_usd': workshop_stones_price_usd,
                    'workshop_stones_price_egp': workshop_stones_price_egp,
                    'customer_stones_weight': customer_stones_weight,
                    'customer_stones_count': customer_stones_count,
                    'customer_stones_installation_egp': customer_stones_installation_egp,
                    'additional_services': additional_services,
                    'additional_services_total_egp': additional_services_total_egp,
                    'subtotal_usd': subtotal_usd,
                    'subtotal_egp': subtotal_egp,
                    'discount_percentage': discount_percentage,
                    'discount_amount_usd': discount_amount_usd,
                    'discount_amount_egp': discount_amount_egp,
                    'total_usd': total_usd,
                    'total_egp': total_egp,
                    'payment_method': payment_method,
                    'paid_amount_usd': paid_amount_usd,
                    'paid_amount_egp': paid_amount_egp,
                    'remaining_amount_usd': remaining_usd,
                    'remaining_amount_egp': remaining_egp,
                    'notes': notes,
                    'invoice_date': date.today(),
                    'status': 'completed' if remaining_usd <= 0 and remaining_egp <= 0 else 'pending'
                }
                
                # حفظ في قاعدة البيانات
                if DATABASE_AVAILABLE:
                    if invoice_ops.add_invoice(invoice_data):
                        ui.alert(
                            title="✅ تم الحفظ بنجاح",
                            description=f"تم حفظ الفاتورة رقم: {invoice_number}",
                            variant="default"
                        )
                    else:
                        ui.alert(
                            title="❌ خطأ في الحفظ",
                            description="فشل في حفظ الفاتورة في قاعدة البيانات",
                            variant="destructive"
                        )
                else:
                    st.warning("⚠️ قاعدة البيانات غير متاحة - لم يتم الحفظ")
            else:
                ui.alert(
                    title="⚠️ بيانات ناقصة",
                    description="يرجى إدخال اسم العميل واسم المنتج على الأقل",
                    variant="warning"
                )
        
        if preview_button:
            st.balloons()
            st.success("🎉 معاينة الفاتورة - قريباً!")
        
        if clear_button:
            st.rerun()

def main():
    """الدالة الرئيسية"""
    invoice_form()

if __name__ == "__main__":
    main()
