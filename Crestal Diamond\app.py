"""
تطبيق Crestal Diamond - نظام إدارة ورشة المجوهرات
الملف الرئيسي لتشغيل التطبيق - الإصدار 3.0 مع قاعدة البيانات MySQL
"""

import streamlit as st
import os
import sys

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

# استيراد وحدات قاعدة البيانات
try:
    from database.database_config import test_connection
    from database.database_operations import customer_ops, invoice_ops, inventory_ops
    DATABASE_AVAILABLE = True
except ImportError as e:
    st.error(f"خطأ في تحميل قاعدة البيانات: {e}")
    DATABASE_AVAILABLE = False

# إعداد الصفحة الرئيسية
st.set_page_config(
    page_title="Crestal Diamond - نظام إدارة ورشة المجوهرات",
    page_icon="💎",
    layout="wide",
    initial_sidebar_state="expanded"
)

# إعداد الشريط الجانبي
st.sidebar.title("💎 Crestal Diamond")

# محاولة عرض الشعار (اختياري)
try:
    st.sidebar.image("assets/logo.png", use_container_width=True)
except:
    pass  # تجاهل الخطأ إذا لم يكن الشعار موجود

# معلومات النظام
st.sidebar.markdown("---")
st.sidebar.markdown("### 📋 معلومات النظام")
st.sidebar.info("""
**نظام إدارة ورشة المجوهرات**
- إنشاء الفواتير
- إدارة حسابات العملاء  
- تتبع المخزون
- التقارير المالية
""")

# الصفحة الرئيسية
def main_page():
    st.title("💎 مرحباً بك في نظام Crestal Diamond - الإصدار 3.0")

    # عرض حالة قاعدة البيانات
    if DATABASE_AVAILABLE:
        if test_connection():
            st.success("🗄️ قاعدة البيانات MySQL متصلة ومتاحة")
        else:
            st.error("❌ خطأ في الاتصال بقاعدة البيانات")
    else:
        st.warning("⚠️ قاعدة البيانات غير متاحة - يعمل النظام في الوضع التقليدي")

    st.divider()

    # إحصائيات سريعة من قاعدة البيانات
    if DATABASE_AVAILABLE:
        st.subheader("📊 إحصائيات سريعة")

        col1, col2, col3, col4 = st.columns(4)

        try:
            # إحصائيات العملاء
            customers_df = customer_ops.get_all_customers()
            total_customers = len(customers_df) if not customers_df.empty else 0

            # إحصائيات الفواتير
            invoices_df = invoice_ops.get_all_invoices()
            total_invoices = len(invoices_df) if not invoices_df.empty else 0
            total_sales_usd = invoices_df['total_usd'].sum() if not invoices_df.empty else 0
            total_sales_egp = invoices_df['total_egp'].sum() if not invoices_df.empty else 0

            # إحصائيات المخزون
            inventory_df = inventory_ops.get_all_inventory()
            total_inventory_items = len(inventory_df) if not inventory_df.empty else 0
            total_inventory_value_usd = inventory_df['total_value_usd'].sum() if not inventory_df.empty else 0

            with col1:
                st.metric("👥 إجمالي العملاء", total_customers)

            with col2:
                st.metric("🧾 إجمالي الفواتير", total_invoices)

            with col3:
                st.metric("💰 إجمالي المبيعات (USD)", f"${total_sales_usd:,.2f}")

            with col4:
                st.metric("📦 عناصر المخزون", total_inventory_items)

            # إحصائيات إضافية
            col5, col6 = st.columns(2)

            with col5:
                st.metric("💵 إجمالي المبيعات (EGP)", f"{total_sales_egp:,.2f} ج.م")

            with col6:
                st.metric("🏪 قيمة المخزون (USD)", f"${total_inventory_value_usd:,.2f}")

        except Exception as e:
            st.error(f"خطأ في جلب الإحصائيات: {e}")

    st.divider()

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        ### 📝 إنشاء الفواتير
        - فواتير الذهب والألماس
        - حساب المصنعية
        - إدارة الأحجار الكريمة
        - الخدمات الإضافية
        - **جديد**: حفظ في قاعدة البيانات MySQL
        """)
        if st.button("🚀 إنشاء فاتورة جديدة", use_container_width=True):
            st.switch_page("pages/invoice.py")
    
    with col2:
        st.markdown("""
        ### 👥 إدارة العملاء
        - حسابات العملاء
        - تتبع المدفوعات
        - الأرصدة والديون
        - تاريخ المعاملات
        - **جديد**: قاعدة بيانات متقدمة
        """)
        if st.button("📊 إدارة العملاء", use_container_width=True):
            st.switch_page("pages/01_customers_data.py")
    
    with col3:
        st.markdown("""
        ### 📦 إدارة المخزون
        - مخزون الذهب
        - مخزون الألماس
        - تتبع الكميات
        - تقارير المخزون
        """)
        if st.button("💰 إدارة المخزون", use_container_width=True):
            st.switch_page("pages/02_gold_inventory.py")

    # إحصائيات سريعة
    st.markdown("---")
    st.subheader("📊 إحصائيات سريعة")
    
    # قراءة البيانات إذا كانت متوفرة
    try:
        import pandas as pd
        if os.path.isfile('invoices.csv'):
            df = pd.read_csv('invoices.csv', encoding='utf-8-sig')
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_customers = len(df['customer_name'].unique()) if not df.empty else 0
                st.metric("إجمالي العملاء", total_customers)
            
            with col2:
                total_invoices = len(df) if not df.empty else 0
                st.metric("إجمالي الفواتير", total_invoices)
            
            with col3:
                total_usd = df['usd_change'].sum() if not df.empty else 0
                st.metric("إجمالي بالدولار", f"$ {total_usd:.2f}")
            
            with col4:
                total_egp = df['egp_change'].sum() if not df.empty else 0
                st.metric("إجمالي بالجنيه", f"{total_egp:.2f} ج.م")
        else:
            st.info("لا توجد بيانات حتى الآن. ابدأ بإنشاء أول فاتورة!")
    except Exception as e:
        st.warning("تعذر تحميل الإحصائيات")

    # معلومات إضافية
    st.markdown("---")
    st.markdown("""
    ### 🎯 ميزات النظام
    
    **💎 إدارة شاملة للمجوهرات:**
    - حساب دقيق للذهب والمصنعية
    - إدارة الأحجار الكريمة (ورشة وعميل)
    - خدمات إضافية متنوعة
    - دعم العملات المتعددة (USD/EGP)
    
    **📊 تتبع مالي متقدم:**
    - حسابات العملاء التفصيلية
    - تتبع المدفوعات والأرصدة
    - تقارير مالية شاملة
    - إحصائيات في الوقت الفعلي
    
    **🔧 سهولة الاستخدام:**
    - واجهة عربية بديهية
    - تصميم متجاوب
    - حفظ تلقائي للبيانات
    - تصدير التقارير
    """)

# تشغيل الصفحة الرئيسية
if __name__ == "__main__":
    main_page()
