"""
تطبيق Crestal Diamond - نظام إدارة ورشة المجوهرات
الملف الرئيسي لتشغيل التطبيق
"""

import streamlit as st
import os

# إعداد الصفحة الرئيسية
st.set_page_config(
    page_title="Crestal Diamond - نظام إدارة ورشة المجوهرات",
    page_icon="💎",
    layout="wide",
    initial_sidebar_state="expanded"
)

# إعداد الشريط الجانبي
st.sidebar.title("💎 Crestal Diamond")

# محاولة عرض الشعار (اختياري)
try:
    st.sidebar.image("assets/logo.png", use_container_width=True)
except:
    pass  # تجاهل الخطأ إذا لم يكن الشعار موجود

# معلومات النظام
st.sidebar.markdown("---")
st.sidebar.markdown("### 📋 معلومات النظام")
st.sidebar.info("""
**نظام إدارة ورشة المجوهرات**
- إنشاء الفواتير
- إدارة حسابات العملاء  
- تتبع المخزون
- التقارير المالية
""")

# الصفحة الرئيسية
def main_page():
    st.title("💎 مرحباً بك في نظام Crestal Diamond")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        ### 📝 إنشاء الفواتير
        - فواتير الذهب والألماس
        - حساب المصنعية
        - إدارة الأحجار الكريمة
        - الخدمات الإضافية
        """)
        if st.button("🚀 إنشاء فاتورة جديدة", use_container_width=True):
            st.switch_page("pages/invoice.py")
    
    with col2:
        st.markdown("""
        ### 👥 إدارة العملاء
        - حسابات العملاء
        - تتبع المدفوعات
        - الأرصدة والديون
        - تاريخ المعاملات
        """)
        if st.button("📊 إدارة العملاء", use_container_width=True):
            st.switch_page("pages/01_customers_data.py")
    
    with col3:
        st.markdown("""
        ### 📦 إدارة المخزون
        - مخزون الذهب
        - مخزون الألماس
        - تتبع الكميات
        - تقارير المخزون
        """)
        if st.button("💰 إدارة المخزون", use_container_width=True):
            st.switch_page("pages/02_gold_inventory.py")

    # إحصائيات سريعة
    st.markdown("---")
    st.subheader("📊 إحصائيات سريعة")
    
    # قراءة البيانات إذا كانت متوفرة
    try:
        import pandas as pd
        if os.path.isfile('invoices.csv'):
            df = pd.read_csv('invoices.csv', encoding='utf-8-sig')
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_customers = len(df['customer_name'].unique()) if not df.empty else 0
                st.metric("إجمالي العملاء", total_customers)
            
            with col2:
                total_invoices = len(df) if not df.empty else 0
                st.metric("إجمالي الفواتير", total_invoices)
            
            with col3:
                total_usd = df['usd_change'].sum() if not df.empty else 0
                st.metric("إجمالي بالدولار", f"$ {total_usd:.2f}")
            
            with col4:
                total_egp = df['egp_change'].sum() if not df.empty else 0
                st.metric("إجمالي بالجنيه", f"{total_egp:.2f} ج.م")
        else:
            st.info("لا توجد بيانات حتى الآن. ابدأ بإنشاء أول فاتورة!")
    except Exception as e:
        st.warning("تعذر تحميل الإحصائيات")

    # معلومات إضافية
    st.markdown("---")
    st.markdown("""
    ### 🎯 ميزات النظام
    
    **💎 إدارة شاملة للمجوهرات:**
    - حساب دقيق للذهب والمصنعية
    - إدارة الأحجار الكريمة (ورشة وعميل)
    - خدمات إضافية متنوعة
    - دعم العملات المتعددة (USD/EGP)
    
    **📊 تتبع مالي متقدم:**
    - حسابات العملاء التفصيلية
    - تتبع المدفوعات والأرصدة
    - تقارير مالية شاملة
    - إحصائيات في الوقت الفعلي
    
    **🔧 سهولة الاستخدام:**
    - واجهة عربية بديهية
    - تصميم متجاوب
    - حفظ تلقائي للبيانات
    - تصدير التقارير
    """)

# تشغيل الصفحة الرئيسية
if __name__ == "__main__":
    main_page()
