"""
🤖 الوكيل الذكي لقاعدة البيانات - نظام كريستال دايموند
Intelligent Database Agent for Crestal Diamond System

هذا الوكيل الذكي مسؤول عن:
- فحص قاعدة البيانات بشكل دوري
- اكتشاف وإصلاح المشاكل تلقائياً
- تحسين الأداء والاستقرار
- مراقبة صحة النظام
- إنشاء تقارير تشخيصية
"""

import mysql.connector
from database_config import DatabaseConfig
from database_operations import customer_ops, invoice_ops, inventory_ops, transaction_ops
import pandas as pd
import logging
from datetime import datetime, date, timedelta
import json
import os
import time
from typing import Dict, List, Tuple, Any, Optional

# إعداد السجلات المتقدم
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_agent.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseIntelligentAgent:
    """الوكيل الذكي لقاعدة البيانات"""
    
    def __init__(self):
        """تهيئة الوكيل الذكي"""
        self.db_config = DatabaseConfig()
        self.last_check_time = None
        self.health_status = {}
        self.performance_metrics = {}
        self.issues_found = []
        self.fixes_applied = []
        
        # معلومات شاملة عن قاعدة البيانات
        self.database_schema = {
            'customers': {
                'columns': ['id', 'customer_name', 'phone', 'address', 'email', 'notes', 'created_at'],
                'primary_key': 'id',
                'indexes': ['customer_name', 'phone'],
                'constraints': ['customer_name NOT NULL'],
                'expected_data_types': {
                    'id': 'int',
                    'customer_name': 'varchar',
                    'phone': 'varchar',
                    'address': 'text',
                    'email': 'varchar',
                    'notes': 'text',
                    'created_at': 'timestamp'
                }
            },
            'invoices': {
                'columns': [
                    'id', 'invoice_number', 'customer_id', 'customer_name', 'phone', 'address',
                    'main_product', 'main_product_price_usd', 'main_product_price_egp',
                    'workshop_stones_weight', 'workshop_stones_count', 'workshop_stones_price_usd',
                    'workshop_stones_price_egp', 'customer_stones_weight', 'customer_stones_count',
                    'customer_stones_installation_egp', 'additional_services', 'additional_services_total_egp',
                    'subtotal_usd', 'subtotal_egp', 'discount_percentage', 'discount_amount_usd',
                    'discount_amount_egp', 'total_usd', 'total_egp', 'payment_method',
                    'paid_amount_usd', 'paid_amount_egp', 'remaining_amount_usd', 'remaining_amount_egp',
                    'notes', 'invoice_date', 'delivery_date', 'status', 'created_at'
                ],
                'primary_key': 'id',
                'indexes': ['invoice_number', 'customer_name', 'invoice_date', 'status'],
                'constraints': ['invoice_number UNIQUE', 'total_usd >= 0', 'total_egp >= 0']
            },
            'inventory': {
                'columns': [
                    'id', 'item_name', 'category', 'description', 'quantity', 'unit_price_usd',
                    'unit_price_egp', 'total_value_usd', 'total_value_egp', 'supplier',
                    'location', 'minimum_stock', 'notes', 'created_at', 'updated_at'
                ],
                'primary_key': 'id',
                'indexes': ['item_name', 'category', 'quantity'],
                'constraints': ['quantity >= 0', 'unit_price_usd >= 0', 'unit_price_egp >= 0']
            },
            'transactions': {
                'columns': [
                    'id', 'customer_name', 'transaction_type', 'amount_usd', 'amount_egp',
                    'transaction_date', 'description', 'created_at'
                ],
                'primary_key': 'id',
                'indexes': ['customer_name', 'transaction_date', 'transaction_type'],
                'constraints': []
            }
        }
        
        # قواعد التحقق من صحة البيانات
        self.validation_rules = {
            'customers': [
                ('customer_name_not_empty', lambda row: row['customer_name'] and row['customer_name'].strip()),
                ('phone_format', lambda row: not row['phone'] or len(row['phone']) >= 10),
                ('email_format', lambda row: not row['email'] or '@' in row['email'])
            ],
            'invoices': [
                ('positive_amounts', lambda row: row['total_usd'] >= 0 and row['total_egp'] >= 0),
                ('valid_date', lambda row: row['invoice_date'] <= date.today()),
                ('customer_exists', self._validate_customer_exists),
                ('calculation_accuracy', self._validate_invoice_calculations)
            ],
            'inventory': [
                ('positive_quantity', lambda row: row['quantity'] >= 0),
                ('positive_prices', lambda row: row['unit_price_usd'] >= 0 and row['unit_price_egp'] >= 0),
                ('stock_alert', lambda row: row['quantity'] > row['minimum_stock'])
            ],
            'transactions': [
                ('valid_transaction_type', lambda row: row['transaction_type'] in ['invoice', 'payment', 'adjustment', 'opening_balance']),
                ('valid_date', lambda row: row['transaction_date'] <= date.today())
            ]
        }
        
        logger.info("🤖 تم تهيئة الوكيل الذكي لقاعدة البيانات")
    
    def get_connection(self) -> mysql.connector.MySQLConnection:
        """إنشاء اتصال آمن بقاعدة البيانات"""
        return mysql.connector.connect(
            host=self.db_config.host,
            database=self.db_config.database,
            user=self.db_config.user,
            password=self.db_config.password,
            port=self.db_config.port,
            autocommit=False
        )
    
    def comprehensive_health_check(self) -> Dict[str, Any]:
        """فحص صحة شامل لقاعدة البيانات"""
        logger.info("🔍 بدء الفحص الصحي الشامل لقاعدة البيانات...")
        
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'connection_status': False,
            'schema_integrity': {},
            'data_validation': {},
            'performance_metrics': {},
            'security_check': {},
            'recommendations': [],
            'issues_found': [],
            'auto_fixes_applied': []
        }
        
        try:
            # 1. فحص الاتصال
            health_report['connection_status'] = self._check_connection()
            
            # 2. فحص سلامة المخطط
            health_report['schema_integrity'] = self._check_schema_integrity()
            
            # 3. فحص صحة البيانات
            health_report['data_validation'] = self._validate_all_data()
            
            # 4. فحص الأداء
            health_report['performance_metrics'] = self._check_performance()
            
            # 5. فحص الأمان
            health_report['security_check'] = self._check_security()
            
            # 6. تطبيق الإصلاحات التلقائية
            health_report['auto_fixes_applied'] = self._apply_auto_fixes()
            
            # 7. إنشاء التوصيات
            health_report['recommendations'] = self._generate_recommendations()
            
            # تحديد الحالة العامة
            if health_report['issues_found']:
                health_report['overall_status'] = 'needs_attention'
            
            self.health_status = health_report
            self.last_check_time = datetime.now()
            
            logger.info("✅ تم إكمال الفحص الصحي الشامل")
            return health_report
            
        except Exception as e:
            logger.error(f"❌ خطأ في الفحص الصحي: {str(e)}")
            health_report['overall_status'] = 'error'
            health_report['issues_found'].append(f"خطأ في الفحص: {str(e)}")
            return health_report
    
    def _check_connection(self) -> bool:
        """فحص حالة الاتصال بقاعدة البيانات"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            connection.close()
            logger.info("✅ الاتصال بقاعدة البيانات سليم")
            return True
        except Exception as e:
            logger.error(f"❌ فشل في الاتصال بقاعدة البيانات: {str(e)}")
            self.issues_found.append(f"فشل الاتصال: {str(e)}")
            return False
    
    def _check_schema_integrity(self) -> Dict[str, Any]:
        """فحص سلامة مخطط قاعدة البيانات"""
        logger.info("🔍 فحص سلامة مخطط قاعدة البيانات...")
        
        schema_report = {
            'tables_exist': {},
            'columns_integrity': {},
            'indexes_status': {},
            'constraints_status': {}
        }
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # فحص وجود الجداول
            for table_name in self.database_schema.keys():
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                exists = cursor.fetchone() is not None
                schema_report['tables_exist'][table_name] = exists
                
                if not exists:
                    self.issues_found.append(f"الجدول {table_name} غير موجود")
                    continue
                
                # فحص الأعمدة
                cursor.execute(f"DESCRIBE {table_name}")
                existing_columns = [row[0] for row in cursor.fetchall()]
                expected_columns = self.database_schema[table_name]['columns']
                
                missing_columns = set(expected_columns) - set(existing_columns)
                extra_columns = set(existing_columns) - set(expected_columns)
                
                schema_report['columns_integrity'][table_name] = {
                    'missing': list(missing_columns),
                    'extra': list(extra_columns),
                    'status': 'ok' if not missing_columns and not extra_columns else 'issues'
                }
                
                if missing_columns:
                    self.issues_found.append(f"أعمدة مفقودة في {table_name}: {missing_columns}")
                
                # فحص الفهارس
                cursor.execute(f"SHOW INDEX FROM {table_name}")
                existing_indexes = [row[2] for row in cursor.fetchall() if row[2] != 'PRIMARY']
                expected_indexes = [f"idx_{idx}" for idx in self.database_schema[table_name]['indexes']]
                
                missing_indexes = set(expected_indexes) - set(existing_indexes)
                schema_report['indexes_status'][table_name] = {
                    'missing': list(missing_indexes),
                    'existing': existing_indexes,
                    'status': 'ok' if not missing_indexes else 'needs_indexes'
                }
            
            cursor.close()
            connection.close()
            
            logger.info("✅ تم فحص سلامة المخطط")
            return schema_report
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص المخطط: {str(e)}")
            self.issues_found.append(f"خطأ في فحص المخطط: {str(e)}")
            return schema_report
    
    def _validate_all_data(self) -> Dict[str, Any]:
        """فحص صحة جميع البيانات"""
        logger.info("🔍 فحص صحة البيانات...")
        
        validation_report = {
            'customers': {'valid': 0, 'invalid': 0, 'issues': []},
            'invoices': {'valid': 0, 'invalid': 0, 'issues': []},
            'inventory': {'valid': 0, 'invalid': 0, 'issues': []},
            'transactions': {'valid': 0, 'invalid': 0, 'issues': []}
        }
        
        try:
            # فحص العملاء
            customers_df = customer_ops.get_all_customers()
            validation_report['customers'] = self._validate_table_data('customers', customers_df)
            
            # فحص الفواتير
            invoices_df = invoice_ops.get_all_invoices()
            validation_report['invoices'] = self._validate_table_data('invoices', invoices_df)
            
            # فحص المخزون
            inventory_df = inventory_ops.get_all_inventory()
            validation_report['inventory'] = self._validate_table_data('inventory', inventory_df)
            
            # فحص المعاملات
            transactions_df = transaction_ops.get_all_transactions()
            validation_report['transactions'] = self._validate_table_data('transactions', transactions_df)
            
            logger.info("✅ تم فحص صحة البيانات")
            return validation_report
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص البيانات: {str(e)}")
            self.issues_found.append(f"خطأ في فحص البيانات: {str(e)}")
            return validation_report
    
    def _validate_table_data(self, table_name: str, df: pd.DataFrame) -> Dict[str, Any]:
        """فحص صحة بيانات جدول محدد"""
        if df.empty:
            return {'valid': 0, 'invalid': 0, 'issues': ['الجدول فارغ']}
        
        validation_result = {'valid': 0, 'invalid': 0, 'issues': []}
        rules = self.validation_rules.get(table_name, [])
        
        for index, row in df.iterrows():
            row_valid = True
            for rule_name, rule_func in rules:
                try:
                    if not rule_func(row):
                        validation_result['issues'].append(f"السجل {index}: فشل في {rule_name}")
                        row_valid = False
                except Exception as e:
                    validation_result['issues'].append(f"السجل {index}: خطأ في {rule_name} - {str(e)}")
                    row_valid = False
            
            if row_valid:
                validation_result['valid'] += 1
            else:
                validation_result['invalid'] += 1
        
        return validation_result
    
    def _validate_customer_exists(self, row) -> bool:
        """التحقق من وجود العميل"""
        if 'customer_name' not in row:
            return True
        
        try:
            customer = customer_ops.get_customer_by_name(row['customer_name'])
            return customer is not None
        except:
            return True  # تجاهل الخطأ في هذا السياق
    
    def _validate_invoice_calculations(self, row) -> bool:
        """التحقق من صحة حسابات الفاتورة"""
        try:
            # التحقق من صحة الحسابات الأساسية
            expected_subtotal_usd = (row.get('main_product_price_usd', 0) + 
                                   row.get('workshop_stones_price_usd', 0))
            
            actual_subtotal_usd = row.get('subtotal_usd', 0)
            
            # السماح بفرق صغير للأرقام العشرية
            return abs(expected_subtotal_usd - actual_subtotal_usd) < 0.01
        except:
            return True  # تجاهل الخطأ في هذا السياق
    
    def _check_performance(self) -> Dict[str, Any]:
        """فحص أداء قاعدة البيانات"""
        logger.info("⚡ فحص أداء قاعدة البيانات...")
        
        performance_report = {
            'query_times': {},
            'table_sizes': {},
            'index_efficiency': {},
            'connection_pool': {},
            'recommendations': []
        }
        
        try:
            # قياس أوقات الاستعلامات
            start_time = time.time()
            customers_df = customer_ops.get_all_customers()
            performance_report['query_times']['customers'] = time.time() - start_time
            
            start_time = time.time()
            invoices_df = invoice_ops.get_all_invoices()
            performance_report['query_times']['invoices'] = time.time() - start_time
            
            start_time = time.time()
            inventory_df = inventory_ops.get_all_inventory()
            performance_report['query_times']['inventory'] = time.time() - start_time
            
            start_time = time.time()
            transactions_df = transaction_ops.get_all_transactions()
            performance_report['query_times']['transactions'] = time.time() - start_time
            
            # حساب أحجام الجداول
            connection = self.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT table_name, table_rows, 
                       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.TABLES 
                WHERE table_schema = %s
            """, (self.db_config.database,))
            
            for table_name, rows, size_mb in cursor.fetchall():
                performance_report['table_sizes'][table_name] = {
                    'rows': rows,
                    'size_mb': float(size_mb) if size_mb else 0
                }
            
            cursor.close()
            connection.close()
            
            # تحليل الأداء وإنشاء التوصيات
            total_query_time = sum(performance_report['query_times'].values())
            if total_query_time > 1.0:
                performance_report['recommendations'].append("أوقات الاستعلام بطيئة - يُنصح بتحسين الفهارس")
            
            logger.info("✅ تم فحص الأداء")
            return performance_report

        except Exception as e:
            logger.error(f"❌ خطأ في فحص الأداء: {str(e)}")
            self.issues_found.append(f"خطأ في فحص الأداء: {str(e)}")
            return performance_report

    def _check_security(self) -> Dict[str, Any]:
        """فحص أمان قاعدة البيانات"""
        logger.info("🔐 فحص أمان قاعدة البيانات...")

        security_report = {
            'user_privileges': {},
            'password_strength': 'unknown',
            'connection_encryption': False,
            'sensitive_data_exposure': [],
            'recommendations': []
        }

        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # فحص صلاحيات المستخدم
            cursor.execute("SHOW GRANTS FOR CURRENT_USER()")
            grants = cursor.fetchall()
            security_report['user_privileges'] = [grant[0] for grant in grants]

            # فحص البيانات الحساسة
            customers_df = customer_ops.get_all_customers()
            if not customers_df.empty:
                # فحص أرقام الهواتف المكشوفة
                exposed_phones = customers_df[customers_df['phone'].notna() & (customers_df['phone'] != '')]['phone'].count()
                if exposed_phones > 0:
                    security_report['sensitive_data_exposure'].append(f"{exposed_phones} رقم هاتف مكشوف")

                # فحص عناوين البريد الإلكتروني
                exposed_emails = customers_df[customers_df['email'].notna() & (customers_df['email'] != '')]['email'].count()
                if exposed_emails > 0:
                    security_report['sensitive_data_exposure'].append(f"{exposed_emails} بريد إلكتروني مكشوف")

            # توصيات أمنية
            security_report['recommendations'] = [
                "استخدام كلمات مرور قوية",
                "تفعيل التشفير للاتصالات",
                "إنشاء نسخ احتياطية منتظمة",
                "مراقبة سجلات الوصول",
                "تحديد صلاحيات المستخدمين"
            ]

            cursor.close()
            connection.close()

            logger.info("✅ تم فحص الأمان")
            return security_report

        except Exception as e:
            logger.error(f"❌ خطأ في فحص الأمان: {str(e)}")
            self.issues_found.append(f"خطأ في فحص الأمان: {str(e)}")
            return security_report

    def _apply_auto_fixes(self) -> List[str]:
        """تطبيق الإصلاحات التلقائية"""
        logger.info("🔧 تطبيق الإصلاحات التلقائية...")

        fixes_applied = []

        try:
            # إصلاح الفهارس المفقودة
            fixes_applied.extend(self._fix_missing_indexes())

            # تنظيف البيانات المكررة
            fixes_applied.extend(self._clean_duplicate_data())

            # إصلاح القيم الفارغة
            fixes_applied.extend(self._fix_null_values())

            # تحديث الإحصائيات
            fixes_applied.extend(self._update_statistics())

            self.fixes_applied = fixes_applied
            logger.info(f"✅ تم تطبيق {len(fixes_applied)} إصلاح تلقائي")
            return fixes_applied

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق الإصلاحات: {str(e)}")
            return fixes_applied

    def _fix_missing_indexes(self) -> List[str]:
        """إصلاح الفهارس المفقودة"""
        fixes = []

        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # فهارس أساسية مهمة
            essential_indexes = [
                "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name)",
                "CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_name)",
                "CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date)",
                "CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_name)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)"
            ]

            for index_sql in essential_indexes:
                try:
                    cursor.execute(index_sql)
                    index_name = index_sql.split("idx_")[1].split(" ")[0]
                    fixes.append(f"تم إنشاء الفهرس: idx_{index_name}")
                except mysql.connector.Error as e:
                    if "Duplicate key name" not in str(e):
                        logger.warning(f"فشل في إنشاء فهرس: {str(e)}")

            connection.commit()
            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"خطأ في إصلاح الفهارس: {str(e)}")

        return fixes

    def _clean_duplicate_data(self) -> List[str]:
        """تنظيف البيانات المكررة"""
        fixes = []

        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # البحث عن العملاء المكررين
            cursor.execute("""
                SELECT customer_name, COUNT(*) as count
                FROM customers
                GROUP BY customer_name
                HAVING COUNT(*) > 1
            """)

            duplicates = cursor.fetchall()
            for customer_name, count in duplicates:
                # الاحتفاظ بأحدث سجل وحذف الباقي
                cursor.execute("""
                    DELETE FROM customers
                    WHERE customer_name = %s
                    AND id NOT IN (
                        SELECT * FROM (
                            SELECT MAX(id) FROM customers
                            WHERE customer_name = %s
                        ) as temp
                    )
                """, (customer_name, customer_name))

                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    fixes.append(f"تم حذف {deleted_count} سجل مكرر للعميل: {customer_name}")

            connection.commit()
            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات المكررة: {str(e)}")

        return fixes

    def _fix_null_values(self) -> List[str]:
        """إصلاح القيم الفارغة"""
        fixes = []

        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # إصلاح أسماء العملاء الفارغة
            cursor.execute("""
                UPDATE customers
                SET customer_name = CONCAT('عميل غير محدد - ', id)
                WHERE customer_name IS NULL OR customer_name = ''
            """)

            if cursor.rowcount > 0:
                fixes.append(f"تم إصلاح {cursor.rowcount} اسم عميل فارغ")

            # إصلاح حالات الفواتير الفارغة
            cursor.execute("""
                UPDATE invoices
                SET status = 'pending'
                WHERE status IS NULL OR status = ''
            """)

            if cursor.rowcount > 0:
                fixes.append(f"تم إصلاح {cursor.rowcount} حالة فاتورة فارغة")

            # إصلاح فئات المخزون الفارغة
            cursor.execute("""
                UPDATE inventory
                SET category = 'غير محدد'
                WHERE category IS NULL OR category = ''
            """)

            if cursor.rowcount > 0:
                fixes.append(f"تم إصلاح {cursor.rowcount} فئة مخزون فارغة")

            connection.commit()
            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"خطأ في إصلاح القيم الفارغة: {str(e)}")

        return fixes

    def _update_statistics(self) -> List[str]:
        """تحديث إحصائيات قاعدة البيانات"""
        fixes = []

        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # تحديث إحصائيات الجداول
            tables = ['customers', 'invoices', 'inventory', 'transactions']

            for table in tables:
                cursor.execute(f"ANALYZE TABLE {table}")
                fixes.append(f"تم تحديث إحصائيات جدول {table}")

            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")

        return fixes

    def _generate_recommendations(self) -> List[str]:
        """إنشاء توصيات لتحسين قاعدة البيانات"""
        recommendations = []

        try:
            # توصيات عامة
            recommendations.extend([
                "إجراء نسخ احتياطية دورية (يومية)",
                "مراقبة أداء الاستعلامات بانتظام",
                "تحديث كلمات المرور بشكل دوري",
                "مراجعة صلاحيات المستخدمين",
                "تنظيف البيانات القديمة والمؤقتة"
            ])

            # توصيات مبنية على حالة البيانات
            customers_df = customer_ops.get_all_customers()
            if len(customers_df) > 1000:
                recommendations.append("تقسيم جدول العملاء لتحسين الأداء")

            invoices_df = invoice_ops.get_all_invoices()
            if len(invoices_df) > 5000:
                recommendations.append("أرشفة الفواتير القديمة")

            # توصيات الأمان
            if self.issues_found:
                recommendations.append("إصلاح المشاكل المكتشفة فوراً")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            return recommendations

    def generate_health_report(self) -> str:
        """إنشاء تقرير صحة مفصل"""
        if not self.health_status:
            self.comprehensive_health_check()

        report = f"""
🤖 تقرير الوكيل الذكي لقاعدة البيانات - نظام كريستال دايموند
{'='*70}

📅 وقت الفحص: {self.health_status.get('timestamp', 'غير محدد')}
🎯 الحالة العامة: {self.health_status.get('overall_status', 'غير معروف')}

🔌 حالة الاتصال: {'✅ متصل' if self.health_status.get('connection_status') else '❌ غير متصل'}

📊 إحصائيات البيانات:
"""

        try:
            customers_count = len(customer_ops.get_all_customers())
            invoices_count = len(invoice_ops.get_all_invoices())
            inventory_count = len(inventory_ops.get_all_inventory())
            transactions_count = len(transaction_ops.get_all_transactions())

            report += f"""   👥 العملاء: {customers_count} سجل
   🧾 الفواتير: {invoices_count} سجل
   📦 المخزون: {inventory_count} سجل
   💳 المعاملات: {transactions_count} سجل

"""
        except:
            report += "   ❌ لا يمكن جلب الإحصائيات\n\n"

        # المشاكل المكتشفة
        if self.issues_found:
            report += "🚨 المشاكل المكتشفة:\n"
            for issue in self.issues_found:
                report += f"   ❌ {issue}\n"
            report += "\n"
        else:
            report += "✅ لم يتم اكتشاف أي مشاكل\n\n"

        # الإصلاحات المطبقة
        if self.fixes_applied:
            report += "🔧 الإصلاحات المطبقة:\n"
            for fix in self.fixes_applied:
                report += f"   ✅ {fix}\n"
            report += "\n"

        # التوصيات
        recommendations = self.health_status.get('recommendations', [])
        if recommendations:
            report += "💡 التوصيات:\n"
            for rec in recommendations:
                report += f"   💡 {rec}\n"
            report += "\n"

        report += f"""
⚡ معلومات الأداء:
   🕐 آخر فحص: {self.last_check_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_check_time else 'لم يتم'}
   📈 حالة النظام: {'مستقر' if not self.issues_found else 'يحتاج انتباه'}

{'='*70}
🤖 الوكيل الذكي جاهز للمراقبة المستمرة
"""

        return report

    def save_report_to_file(self, filename: str = None) -> str:
        """حفظ التقرير في ملف"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"database_health_report_{timestamp}.txt"

        report = self.generate_health_report()

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)

            logger.info(f"✅ تم حفظ التقرير في: {filename}")
            return filename

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ التقرير: {str(e)}")
            return ""

    def monitor_continuously(self, interval_minutes: int = 60):
        """مراقبة مستمرة لقاعدة البيانات"""
        logger.info(f"🔄 بدء المراقبة المستمرة كل {interval_minutes} دقيقة...")

        while True:
            try:
                logger.info("🔍 إجراء فحص دوري...")
                self.comprehensive_health_check()

                if self.issues_found:
                    logger.warning(f"⚠️ تم اكتشاف {len(self.issues_found)} مشكلة")
                    # يمكن إضافة إشعارات هنا

                logger.info(f"😴 انتظار {interval_minutes} دقيقة للفحص التالي...")
                time.sleep(interval_minutes * 60)

            except KeyboardInterrupt:
                logger.info("🛑 تم إيقاف المراقبة المستمرة")
                break
            except Exception as e:
                logger.error(f"❌ خطأ في المراقبة المستمرة: {str(e)}")
                time.sleep(60)  # انتظار دقيقة واحدة قبل المحاولة مرة أخرى


# إنشاء مثيل الوكيل الذكي
db_agent = DatabaseIntelligentAgent()


def main():
    """تشغيل الوكيل الذكي"""
    print("🤖 مرحباً بك في الوكيل الذكي لقاعدة البيانات - نظام كريستال دايموند")
    print("=" * 70)

    while True:
        print("\n🎯 الخيارات المتاحة:")
        print("1. فحص صحة شامل")
        print("2. إنشاء تقرير مفصل")
        print("3. حفظ التقرير في ملف")
        print("4. بدء المراقبة المستمرة")
        print("5. خروج")

        choice = input("\n👆 اختر رقم الخيار: ").strip()

        if choice == "1":
            print("\n🔍 جاري إجراء فحص صحة شامل...")
            health_report = db_agent.comprehensive_health_check()
            print(f"\n✅ تم الفحص - الحالة: {health_report['overall_status']}")

        elif choice == "2":
            print("\n📋 إنشاء تقرير مفصل...")
            report = db_agent.generate_health_report()
            print(report)

        elif choice == "3":
            print("\n💾 حفظ التقرير...")
            filename = db_agent.save_report_to_file()
            if filename:
                print(f"✅ تم حفظ التقرير في: {filename}")

        elif choice == "4":
            interval = input("⏰ أدخل فترة المراقبة بالدقائق (افتراضي: 60): ").strip()
            interval = int(interval) if interval.isdigit() else 60
            db_agent.monitor_continuously(interval)

        elif choice == "5":
            print("👋 شكراً لاستخدام الوكيل الذكي!")
            break

        else:
            print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")


if __name__ == "__main__":
    main()
