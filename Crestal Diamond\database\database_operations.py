"""
🗄️ عمليات قاعدة البيانات - نظام كريستال دايموند
Database Operations for Crestal Diamond System
"""

try:
    from .database_config import execute_query, db_config
except ImportError:
    from database_config import execute_query, db_config
import pandas as pd
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class CustomerOperations:
    """عمليات العملاء"""
    
    @staticmethod
    def add_customer(customer_data):
        """إضافة عميل جديد"""
        query = """
        INSERT INTO customers (customer_name, phone, address, email, notes)
        VALUES (%s, %s, %s, %s, %s)
        """
        params = (
            customer_data.get('customer_name'),
            customer_data.get('phone'),
            customer_data.get('address'),
            customer_data.get('email'),
            customer_data.get('notes')
        )
        
        return execute_query(query, params)
    
    @staticmethod
    def get_all_customers():
        """الحصول على جميع العملاء"""
        query = "SELECT * FROM customers ORDER BY customer_name"
        result = execute_query(query, fetch=True)
        return pd.DataFrame(result) if result else pd.DataFrame()
    
    @staticmethod
    def get_customer_by_id(customer_id):
        """الحصول على عميل بالمعرف"""
        query = "SELECT * FROM customers WHERE id = %s"
        result = execute_query(query, (customer_id,), fetch=True)
        return result[0] if result else None

    @staticmethod
    def get_customer_by_name(customer_name):
        """الحصول على عميل بالاسم"""
        query = "SELECT * FROM customers WHERE customer_name = %s"
        result = execute_query(query, (customer_name,), fetch=True)
        return result[0] if result else None
    
    @staticmethod
    def update_customer_balance(customer_id, balance_change):
        """تحديث رصيد العميل"""
        query = """
        UPDATE customers 
        SET total_balance = total_balance + %s 
        WHERE id = %s
        """
        return execute_query(query, (balance_change, customer_id))

class InvoiceOperations:
    """عمليات الفواتير"""
    
    @staticmethod
    def add_invoice(invoice_data):
        """إضافة فاتورة جديدة"""
        query = """
        INSERT INTO invoices (
            invoice_number, customer_id, customer_name, phone, address,
            main_product, main_product_price_usd, main_product_price_egp,
            workshop_stones_weight, workshop_stones_count, 
            workshop_stones_price_usd, workshop_stones_price_egp,
            customer_stones_weight, customer_stones_count, 
            customer_stones_installation_egp,
            additional_services, additional_services_total_egp,
            subtotal_usd, subtotal_egp, discount_percentage,
            discount_amount_usd, discount_amount_egp,
            total_usd, total_egp, payment_method,
            paid_amount_usd, paid_amount_egp,
            remaining_amount_usd, remaining_amount_egp,
            notes, invoice_date, delivery_date, status
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s
        )
        """
        
        params = (
            invoice_data.get('invoice_number'),
            invoice_data.get('customer_id'),
            invoice_data.get('customer_name'),
            invoice_data.get('phone'),
            invoice_data.get('address'),
            invoice_data.get('main_product'),
            invoice_data.get('main_product_price_usd', 0),
            invoice_data.get('main_product_price_egp', 0),
            invoice_data.get('workshop_stones_weight', 0),
            invoice_data.get('workshop_stones_count', 0),
            invoice_data.get('workshop_stones_price_usd', 0),
            invoice_data.get('workshop_stones_price_egp', 0),
            invoice_data.get('customer_stones_weight', 0),
            invoice_data.get('customer_stones_count', 0),
            invoice_data.get('customer_stones_installation_egp', 0),
            invoice_data.get('additional_services'),
            invoice_data.get('additional_services_total_egp', 0),
            invoice_data.get('subtotal_usd', 0),
            invoice_data.get('subtotal_egp', 0),
            invoice_data.get('discount_percentage', 0),
            invoice_data.get('discount_amount_usd', 0),
            invoice_data.get('discount_amount_egp', 0),
            invoice_data.get('total_usd', 0),
            invoice_data.get('total_egp', 0),
            invoice_data.get('payment_method'),
            invoice_data.get('paid_amount_usd', 0),
            invoice_data.get('paid_amount_egp', 0),
            invoice_data.get('remaining_amount_usd', 0),
            invoice_data.get('remaining_amount_egp', 0),
            invoice_data.get('notes'),
            invoice_data.get('invoice_date'),
            invoice_data.get('delivery_date'),
            invoice_data.get('status', 'pending')
        )
        
        return execute_query(query, params)
    
    @staticmethod
    def get_all_invoices():
        """الحصول على جميع الفواتير"""
        query = """
        SELECT * FROM invoices 
        ORDER BY invoice_date DESC, created_at DESC
        """
        result = execute_query(query, fetch=True)
        return pd.DataFrame(result) if result else pd.DataFrame()
    
    @staticmethod
    def get_invoice_by_number(invoice_number):
        """الحصول على فاتورة برقم الفاتورة"""
        query = "SELECT * FROM invoices WHERE invoice_number = %s"
        result = execute_query(query, (invoice_number,), fetch=True)
        return result[0] if result else None

class InventoryOperations:
    """عمليات المخزون"""
    
    @staticmethod
    def add_inventory_item(item_data):
        """إضافة عنصر مخزون جديد"""
        query = """
        INSERT INTO inventory (
            item_name, category, description, quantity,
            unit_price_usd, unit_price_egp, total_value_usd, total_value_egp,
            supplier, location, minimum_stock, notes
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            item_data.get('item_name'),
            item_data.get('category'),
            item_data.get('description'),
            item_data.get('quantity', 0),
            item_data.get('unit_price_usd', 0),
            item_data.get('unit_price_egp', 0),
            item_data.get('total_value_usd', 0),
            item_data.get('total_value_egp', 0),
            item_data.get('supplier'),
            item_data.get('location'),
            item_data.get('minimum_stock', 0),
            item_data.get('notes')
        )
        
        return execute_query(query, params)
    
    @staticmethod
    def get_all_inventory():
        """الحصول على جميع عناصر المخزون"""
        query = "SELECT * FROM inventory ORDER BY item_name"
        result = execute_query(query, fetch=True)
        return pd.DataFrame(result) if result else pd.DataFrame()

class TransactionOperations:
    """عمليات المعاملات المالية"""
    
    @staticmethod
    def add_transaction(transaction_data):
        """إضافة معاملة مالية جديدة"""
        # أولاً، الحصول على customer_id من اسم العميل
        customer_name = transaction_data.get('customer_name')
        if customer_name:
            customer_query = "SELECT id FROM customers WHERE customer_name = %s"
            customer_result = execute_query(customer_query, (customer_name,), fetch=True)
            if customer_result:
                customer_id = customer_result[0]['id']
            else:
                # إذا لم يوجد العميل، إنشاؤه أولاً
                CustomerOperations.add_customer({'customer_name': customer_name})
                customer_result = execute_query(customer_query, (customer_name,), fetch=True)
                customer_id = customer_result[0]['id'] if customer_result else None
        else:
            customer_id = transaction_data.get('customer_id')

        query = """
        INSERT INTO transactions (
            customer_id, transaction_type, amount_usd, amount_egp,
            transaction_date, description
        ) VALUES (%s, %s, %s, %s, %s, %s)
        """

        params = (
            customer_id,
            transaction_data.get('transaction_type'),
            transaction_data.get('amount_usd', 0),
            transaction_data.get('amount_egp', 0),
            transaction_data.get('transaction_date'),
            transaction_data.get('description')
        )

        return execute_query(query, params)
    
    @staticmethod
    def get_customer_transactions(customer_name):
        """الحصول على معاملات عميل معين بالاسم"""
        query = """
        SELECT t.*, c.customer_name
        FROM transactions t
        JOIN customers c ON t.customer_id = c.id
        WHERE c.customer_name = %s
        ORDER BY t.transaction_date DESC
        """
        result = execute_query(query, (customer_name,), fetch=True)
        return pd.DataFrame(result) if result else pd.DataFrame()

    @staticmethod
    def get_all_transactions():
        """الحصول على جميع المعاملات"""
        query = "SELECT * FROM transactions ORDER BY transaction_date DESC"
        result = execute_query(query, fetch=True)
        return pd.DataFrame(result) if result else pd.DataFrame()


# إنشاء مثيلات العمليات
customer_ops = CustomerOperations()
invoice_ops = InvoiceOperations()
inventory_ops = InventoryOperations()
transaction_ops = TransactionOperations()
