"""
🧪 اختبار مبسط لقاعدة البيانات - نظام كريستال دايموند
Simple Database Test for Crestal Diamond System
"""

from database_config import db_config, test_connection
from database_operations import customer_ops, invoice_ops, inventory_ops, transaction_ops
from datetime import datetime, date
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_connection_simple():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔌 اختبار الاتصال بقاعدة البيانات...")
    
    if db_config.connect():
        print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
        return True
    else:
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return False

def test_customer_operations():
    """اختبار عمليات العملاء"""
    print("\n👥 اختبار عمليات العملاء...")
    
    # إضافة عميل تجريبي
    test_customer = {
        'customer_name': 'عميل تجريبي - اختبار',
        'phone': '01234567890',
        'address': 'عنوان تجريبي للاختبار',
        'email': '<EMAIL>',
        'notes': 'عميل تجريبي للاختبار'
    }
    
    if customer_ops.add_customer(test_customer):
        print("✅ تم إضافة العميل التجريبي بنجاح")
    else:
        print("❌ فشل في إضافة العميل التجريبي")
        return False
    
    # جلب جميع العملاء
    customers_df = customer_ops.get_all_customers()
    if not customers_df.empty:
        print(f"✅ تم جلب {len(customers_df)} عميل من قاعدة البيانات")
        print(f"📊 أسماء العملاء: {customers_df['customer_name'].tolist()}")
    else:
        print("⚠️ لا توجد بيانات عملاء")
    
    return True

def test_inventory_operations():
    """اختبار عمليات المخزون"""
    print("\n📦 اختبار عمليات المخزون...")
    
    # إضافة عنصر مخزون تجريبي
    test_item = {
        'item_name': 'ذهب عيار 21 - اختبار',
        'category': 'ذهب',
        'description': 'عنصر تجريبي للاختبار',
        'quantity': 10,
        'unit_price_usd': 50.0,
        'unit_price_egp': 1500.0,
        'total_value_usd': 500.0,
        'total_value_egp': 15000.0,
        'supplier': 'مورد تجريبي',
        'location': 'مخزن رئيسي',
        'minimum_stock': 5,
        'notes': 'عنصر تجريبي للاختبار'
    }
    
    if inventory_ops.add_inventory_item(test_item):
        print("✅ تم إضافة عنصر المخزون التجريبي بنجاح")
    else:
        print("❌ فشل في إضافة عنصر المخزون التجريبي")
        return False
    
    # جلب جميع عناصر المخزون
    inventory_df = inventory_ops.get_all_inventory()
    if not inventory_df.empty:
        print(f"✅ تم جلب {len(inventory_df)} عنصر من المخزون")
        print(f"📊 عناصر المخزون: {inventory_df['item_name'].tolist()}")
    else:
        print("⚠️ لا توجد بيانات مخزون")
    
    return True

def test_invoice_operations():
    """اختبار عمليات الفواتير"""
    print("\n🧾 اختبار عمليات الفواتير...")
    
    # إضافة فاتورة تجريبية
    test_invoice = {
        'invoice_number': f'TEST-{datetime.now().strftime("%Y%m%d%H%M%S")}',
        'customer_name': 'عميل تجريبي - اختبار',
        'phone': '01234567890',
        'address': 'عنوان تجريبي',
        'main_product': 'خاتم ذهب - اختبار',
        'main_product_price_usd': 100.0,
        'main_product_price_egp': 3000.0,
        'workshop_stones_weight': 2.5,
        'workshop_stones_count': 5,
        'workshop_stones_price_usd': 50.0,
        'workshop_stones_price_egp': 1500.0,
        'customer_stones_weight': 1.0,
        'customer_stones_count': 2,
        'customer_stones_installation_egp': 500.0,
        'additional_services': 'تنظيف وتلميع',
        'additional_services_total_egp': 200.0,
        'subtotal_usd': 150.0,
        'subtotal_egp': 5200.0,
        'discount_percentage': 5.0,
        'discount_amount_usd': 7.5,
        'discount_amount_egp': 260.0,
        'total_usd': 142.5,
        'total_egp': 4940.0,
        'payment_method': 'نقدي',
        'paid_amount_usd': 100.0,
        'paid_amount_egp': 3000.0,
        'remaining_amount_usd': 42.5,
        'remaining_amount_egp': 1940.0,
        'notes': 'فاتورة تجريبية للاختبار',
        'invoice_date': date.today(),
        'delivery_date': None,
        'status': 'pending'
    }
    
    if invoice_ops.add_invoice(test_invoice):
        print("✅ تم إضافة الفاتورة التجريبية بنجاح")
    else:
        print("❌ فشل في إضافة الفاتورة التجريبية")
        return False
    
    # جلب جميع الفواتير
    invoices_df = invoice_ops.get_all_invoices()
    if not invoices_df.empty:
        print(f"✅ تم جلب {len(invoices_df)} فاتورة من قاعدة البيانات")
        print(f"📊 أرقام الفواتير: {invoices_df['invoice_number'].tolist()}")
        
        # إحصائيات سريعة
        total_usd = invoices_df['total_usd'].sum()
        total_egp = invoices_df['total_egp'].sum()
        print(f"💰 إجمالي المبيعات: ${total_usd:.2f} USD | {total_egp:.2f} EGP")
    else:
        print("⚠️ لا توجد بيانات فواتير")
    
    return True

def test_transaction_operations():
    """اختبار عمليات المعاملات"""
    print("\n💳 اختبار عمليات المعاملات...")
    
    # إضافة معاملة تجريبية
    test_transaction = {
        'customer_id': 1,  # افتراض وجود عميل بمعرف 1
        'invoice_id': 1,   # افتراض وجود فاتورة بمعرف 1
        'transaction_type': 'payment',
        'amount_usd': 50.0,
        'amount_egp': 1500.0,
        'payment_method': 'نقدي',
        'transaction_date': date.today(),
        'description': 'دفعة جزئية - اختبار',
        'reference_number': f'PAY-{datetime.now().strftime("%Y%m%d%H%M%S")}'
    }
    
    if transaction_ops.add_transaction(test_transaction):
        print("✅ تم إضافة المعاملة التجريبية بنجاح")
    else:
        print("❌ فشل في إضافة المعاملة التجريبية")
        return False
    
    # جلب معاملات العميل
    transactions_df = transaction_ops.get_customer_transactions(1)
    if not transactions_df.empty:
        print(f"✅ تم جلب {len(transactions_df)} معاملة للعميل")
    else:
        print("⚠️ لا توجد معاملات للعميل")
    
    return True

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار قاعدة البيانات الشامل...")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 5
    
    # اختبار الاتصال
    if test_connection_simple():
        tests_passed += 1
    
    # اختبار عمليات العملاء
    if test_customer_operations():
        tests_passed += 1
    
    # اختبار عمليات المخزون
    if test_inventory_operations():
        tests_passed += 1
    
    # اختبار عمليات الفواتير
    if test_invoice_operations():
        tests_passed += 1
    
    # اختبار عمليات المعاملات
    if test_transaction_operations():
        tests_passed += 1
    
    # تقرير النتائج
    print("\n" + "=" * 50)
    print(f"📊 تقرير الاختبار النهائي:")
    print(f"✅ نجح: {tests_passed}/{total_tests} اختبار")
    print(f"❌ فشل: {total_tests - tests_passed}/{total_tests} اختبار")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل مثالي!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    # إغلاق الاتصال
    db_config.disconnect()
    
    if success:
        print("\n🏁 انتهى الاختبار بنجاح!")
    else:
        print("\n🚨 انتهى الاختبار مع وجود أخطاء!")
