 
import streamlit as st
import pandas as pd
import os
from datetime import datetime

st.set_page_config(layout="wide", page_title="مخزون الألماس")
st.title("💎 إدارة مخزون الألماس")

DIAMOND_FILE_PATH = 'diamond_inventory.csv'

# --- دالة لحفظ حركة الألماس ---
def save_diamond_transaction(date, desc, carats_change, quality):
    columns = ["date", "description", "carats_change", "quality"]
    new_data = pd.DataFrame([[date, desc, carats_change, quality]], columns=columns)
    
    if not os.path.isfile(DIAMOND_FILE_PATH):
        new_data.to_csv(DIAMOND_FILE_PATH, index=False, encoding='utf-8-sig')
    else:
        new_data.to_csv(DIAMOND_FILE_PATH, mode='a', header=False, index=False, encoding='utf-8-sig')

# --- نموذج لإضافة مخزون جديد ---
with st.expander("➕ إضافة رصيد ألماس جديد"):
    with st.form("diamond_form", clear_on_submit=True):
        d_date = st.date_input("تاريخ الإضافة")
        d_desc = st.text_input("البيان", placeholder="مثال: شراء طرد ألماس")
        
        c1, c2 = st.columns(2)
        with c1:
            d_carats = st.number_input("الوزن بالقيراط", min_value=0.0, format="%.3f")
        with c2:
            d_quality = st.text_input("الجودة (Quality)", placeholder="مثال: VS1, G Color")
            
        submitted = st.form_submit_button("حفظ الإضافة")
        if submitted:
            save_diamond_transaction(str(d_date), d_desc, d_carats, d_quality)
            st.success(f"تمت إضافة {d_carats} قيراط من الألماس إلى المخزون.")

st.write("---")

# --- عرض الرصيد الحالي ---
st.header("📊 الرصيد الحالي للألماس")

if not os.path.isfile(DIAMOND_FILE_PATH):
    st.info("لا يوجد مخزون مسجل بعد.")
else:
    df = pd.read_csv(DIAMOND_FILE_PATH)
    
    total_carats = df['carats_change'].sum()
    st.metric("إجمالي رصيد الألماس", f"{total_carats:.3f} قيراط")
    
    st.write("---")
    
    # عرض سجل الحركات
    st.subheader("📜 سجل حركات الألماس")
    st.dataframe(df.sort_values(by='date', ascending=False), use_container_width=True)