"""
🎨 مكونات UI حديثة مشتركة - نظام كريستال دايموند
Modern UI Components for Crestal Diamond System
"""

import streamlit as st

def get_modern_css():
    """إرجاع CSS الحديث المشترك"""
    return """
    <style>
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            color: white;
            text-align: center;
        }
        
        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
        }
        
        .feature-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .calculation-box {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 1.5rem;
            border-radius: 12px;
            border: 2px solid #dee2e6;
            margin: 1rem 0;
        }
        
        .total-box {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin: 1rem 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin: 0.5rem;
        }
        
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .sidebar-logo {
            text-align: center;
            padding: 2rem 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: -1rem -1rem 2rem -1rem;
            border-radius: 0 0 15px 15px;
        }
        
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .action-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    </style>
    """

def custom_alert(title, description, variant="info"):
    """مكون تنبيه مخصص"""
    colors = {
        "info": {"bg": "#d1ecf1", "border": "#bee5eb", "text": "#0c5460"},
        "success": {"bg": "#d4edda", "border": "#c3e6cb", "text": "#155724"},
        "warning": {"bg": "#fff3cd", "border": "#ffeaa7", "text": "#856404"},
        "error": {"bg": "#f8d7da", "border": "#f5c6cb", "text": "#721c24"}
    }
    
    color = colors.get(variant, colors["info"])
    
    st.markdown(f"""
    <div style="
        background-color: {color['bg']};
        border: 1px solid {color['border']};
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        color: {color['text']};
    ">
        <strong>{title}</strong><br>
        {description}
    </div>
    """, unsafe_allow_html=True)

def custom_metric_card(title, value, description):
    """بطاقة مقياس مخصصة"""
    st.markdown(f"""
    <div class="metric-card">
        <h3 style="margin: 0; color: #667eea;">{title}</h3>
        <h2 style="margin: 0.5rem 0; color: #2c3e50;">{value}</h2>
        <p style="margin: 0; color: #6c757d; font-size: 0.9rem;">{description}</p>
    </div>
    """, unsafe_allow_html=True)

def page_header(title, subtitle=""):
    """رأس الصفحة الحديث"""
    st.markdown(f"""
    <div class="main-header">
        <h1>{title}</h1>
        {f'<p>{subtitle}</p>' if subtitle else ''}
    </div>
    """, unsafe_allow_html=True)

def form_section_start(title):
    """بداية قسم النموذج"""
    st.markdown(f'<div class="form-section">', unsafe_allow_html=True)
    st.subheader(title)

def form_section_end():
    """نهاية قسم النموذج"""
    st.markdown('</div>', unsafe_allow_html=True)

def calculation_box_start():
    """بداية صندوق الحسابات"""
    st.markdown('<div class="calculation-box">', unsafe_allow_html=True)

def calculation_box_end():
    """نهاية صندوق الحسابات"""
    st.markdown('</div>', unsafe_allow_html=True)

def total_box(title, amount_usd, amount_egp):
    """صندوق المجموع النهائي"""
    st.markdown(f"""
    <div class="total-box">
        <h3>{title}</h3>
        <h2>${amount_usd:.2f} USD</h2>
        <h2>{amount_egp:.2f} ج.م</h2>
    </div>
    """, unsafe_allow_html=True)

def status_badge(text, status="success"):
    """شارة الحالة"""
    class_name = f"status-{status}"
    st.markdown(f'<span class="status-badge {class_name}">{text}</span>', unsafe_allow_html=True)

def sidebar_logo(title, subtitle=""):
    """شعار الشريط الجانبي"""
    st.markdown(f"""
    <div class="sidebar-logo">
        <h2 style="color: white; margin: 0;">{title}</h2>
        {f'<p style="color: #e9ecef; margin: 0; font-size: 0.9rem;">{subtitle}</p>' if subtitle else ''}
    </div>
    """, unsafe_allow_html=True)

def data_table_wrapper_start():
    """بداية غلاف جدول البيانات"""
    st.markdown('<div class="data-table">', unsafe_allow_html=True)

def data_table_wrapper_end():
    """نهاية غلاف جدول البيانات"""
    st.markdown('</div>', unsafe_allow_html=True)

def modern_button(text, key, variant="primary"):
    """زر حديث مخصص"""
    if variant == "primary":
        return st.button(text, key=key, use_container_width=True, type="primary")
    else:
        return st.button(text, key=key, use_container_width=True)

def feature_card(title, content, button_text="", button_key="", button_action=None):
    """بطاقة ميزة تفاعلية"""
    st.markdown(f"""
    <div class="feature-card">
        <h3>{title}</h3>
        {content}
    </div>
    """, unsafe_allow_html=True)
    
    if button_text and button_key:
        if st.button(button_text, key=button_key, use_container_width=True):
            if button_action:
                button_action()

def apply_modern_css():
    """تطبيق CSS الحديث"""
    st.markdown(get_modern_css(), unsafe_allow_html=True)

def page_footer():
    """تذييل الصفحة"""
    from datetime import datetime
    st.divider()
    st.markdown(f"""
    <div style="text-align: center; padding: 2rem; color: #6c757d;">
        <p>💎 <strong>Crestal Diamond</strong> - حيث تلتقي التكنولوجيا بالجمال</p>
        <p style="font-size: 0.8rem;">الإصدار 3.0 - آخر تحديث: {datetime.now().strftime("%Y-%m-%d")}</p>
    </div>
    """, unsafe_allow_html=True)
