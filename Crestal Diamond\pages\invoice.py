import streamlit as st
import math
import pandas as pd
import os
from datetime import datetime

# =================================================================================
# 1. إعدادات وإدارة البيانات (الجزء الخلفي)
# =================================================================================

st.set_page_config(layout="wide", page_title="نظام ورشة Crestal Diamond")
FILE_PATH = 'invoices.csv'

# تحسين الأداء - تخزين مؤقت للبيانات
@st.cache_data(ttl=60)  # تخزين مؤقت لمدة 60 ثانية
def load_data_cached():
    """تحميل البيانات مع تخزين مؤقت لتحسين الأداء"""
    try:
        if os.path.isfile(FILE_PATH):
            df = pd.read_csv(FILE_PATH, encoding='utf-8-sig')
            return df
        else:
            # إنشاء DataFrame فارغ بالأعمدة المطلوبة
            columns = ["customer_name", "date", "description", "gold_change", "usd_change", "egp_change"]
            return pd.DataFrame(columns=columns)
    except Exception as e:
        st.error(f"خطأ في تحميل البيانات: {str(e)}")
        columns = ["customer_name", "date", "description", "gold_change", "usd_change", "egp_change"]
        return pd.DataFrame(columns=columns)

@st.cache_data(ttl=300)  # تخزين مؤقت لمدة 5 دقائق
def get_customer_list():
    """الحصول على قائمة العملاء مع تخزين مؤقت"""
    try:
        df = load_data_cached()
        if not df.empty:
            customers = sorted(df['customer_name'].unique().tolist())
            return customers
        return []
    except Exception:
        return []

# --- دالة موحدة لحفظ أي معاملة ---
def save_transaction(customer, date, desc, gold, usd, egp):
    """
    تحفظ أي معاملة (فاتورة, دفعة, رصيد) في ملف CSV.
    """
    try:
        columns = ["customer_name", "date", "description", "gold_change", "usd_change", "egp_change"]
        new_data = pd.DataFrame([[customer, date, desc, gold, usd, egp]], columns=columns)
        
        if not os.path.isfile(FILE_PATH):
            new_data.to_csv(FILE_PATH, index=False, encoding='utf-8-sig')
        else:
            new_data.to_csv(FILE_PATH, mode='a', header=False, index=False, encoding='utf-8-sig')
        # مسح التخزين المؤقت لإعادة تحميل البيانات الجديدة
        st.cache_data.clear()

        return True
    except Exception as e:
        st.error(f"خطأ في حفظ البيانات: {e}")
        return False

# =================================================================================
# 2. تعريف الصفحات (الواجهات الرسومية)
# =================================================================================

def create_invoice_page():
    """
    واجهة إنشاء فاتورة جديدة (مع الميزات الجديدة).
    """
    st.header("📄 إنشاء فاتورة جديدة")

    with st.container(border=True):
        st.subheader("معلومات الفاتورة الأساسية")
        c1, c2, c3 = st.columns(3)
        with c1:
            customer_name = st.text_input("👤 اسم العميل:")
        with c2:
            invoice_date = st.date_input("🗓️ تاريخ الفاتورة")
        with c3:
            exchange_rate = st.number_input(
                "💵 سعر صرف الدولار",
                min_value=0.0,
                format="%.2f",
                help="هذا السعر للعلم فقط ولن يؤثر على الحسابات"
            )

        invoice_description = st.text_input("📝 بيان الفاتورة", placeholder="مثال: خاتم سوليتير موديل 789")

    with st.container(border=True):
        st.subheader("⚖️ حساب الذهب والمصنعية")

        st.markdown("**الحساب بالدولار ($) - للقطع الألماس**")
        w_col1, w_col2, w_col3 = st.columns([2, 2, 1])
        with w_col1:
            gold_weight_usd = st.number_input("وزن الذهب (جرام)", key="gold_usd", min_value=0.0)
        with w_col2:
            workmanship_price_usd = st.number_input("سعر مصنعية الجرام ($)", key="work_usd", min_value=0.0)
        workmanship_subtotal_usd = gold_weight_usd * workmanship_price_usd
        with w_col3:
            st.metric("ناتج المصنعية ($)", f"$ {workmanship_subtotal_usd:.2f}")

        st.markdown("**الحساب بالجنيه (EGP) - للقطع الزركون/العادية**")
        e_w_col1, e_w_col2, e_w_col3 = st.columns([2, 2, 1])
        with e_w_col1:
            gold_weight_egp = st.number_input("وزن الذهب (جرام)", key="gold_egp", min_value=0.0)
        with e_w_col2:
            workmanship_price_egp = st.number_input("سعر مصنعية الجرام (EGP)", key="work_egp", min_value=0.0)
        workmanship_subtotal_egp = gold_weight_egp * workmanship_price_egp
        with e_w_col3:
            st.metric("ناتج المصنعية (ج.م)", f"{workmanship_subtotal_egp:.2f} ج.م")

    # إضافة قسم للأحجار الكريمة والألماس المحسن
    with st.container(border=True):
        st.subheader("💎 الأحجار الكريمة والألماس")

        # قسم أحجار الورشة
        st.markdown("**🏪 أحجار الورشة (بالدولار)**")
        workshop_stones_col1, workshop_stones_col2, workshop_stones_col3, workshop_stones_col4 = st.columns(4)

        with workshop_stones_col1:
            workshop_stone_weight = st.number_input("وزن أحجار الورشة (قيراط)", key="workshop_weight", min_value=0.0, format="%.3f")
        with workshop_stones_col2:
            workshop_stone_count = st.number_input("عدد الأحجار", key="workshop_count", min_value=0, step=1)
        with workshop_stones_col3:
            workshop_stone_price = st.number_input("سعر حساب الأحجار ($)", key="workshop_price", min_value=0.0, format="%.2f")
        with workshop_stones_col4:
            workshop_stones_total = workshop_stone_weight * workshop_stone_price
            st.metric("إجمالي أحجار الورشة", f"$ {workshop_stones_total:.2f}")

        st.write("---")

        # قسم أحجار العميل
        st.markdown("**👤 أحجار العميل (بالجنيه المصري)**")
        client_stones_col1, client_stones_col2, client_stones_col3, client_stones_col4 = st.columns(4)

        with client_stones_col1:
            client_stone_weight = st.number_input("وزن أحجار العميل (قيراط)", key="client_weight", min_value=0.0, format="%.3f")
        with client_stones_col2:
            client_stone_count = st.number_input("عدد أحجار العميل", key="client_count", min_value=0, step=1)
        with client_stones_col3:
            stone_setting_price = st.number_input("حساب تركيب الأحجار (ج.م)", key="setting_price", min_value=0.0, format="%.2f")
        with client_stones_col4:
            client_stones_total = client_stone_count * stone_setting_price
            st.metric("إجمالي تركيب الأحجار", f"{client_stones_total:.2f} ج.م")

        # ملخص الأحجار
        st.write("---")
        st.markdown("**📊 ملخص الأحجار**")
        summary_stones_col1, summary_stones_col2, summary_stones_col3, summary_stones_col4 = st.columns(4)

        with summary_stones_col1:
            total_stone_weight = workshop_stone_weight + client_stone_weight
            st.metric("إجمالي الوزن", f"{total_stone_weight:.3f} قيراط")
        with summary_stones_col2:
            total_stone_count = workshop_stone_count + client_stone_count
            st.metric("إجمالي العدد", f"{total_stone_count} حجر")
        with summary_stones_col3:
            st.metric("قيمة أحجار الورشة", f"$ {workshop_stones_total:.2f}")
        with summary_stones_col4:
            st.metric("قيمة تركيب العميل", f"{client_stones_total:.2f} ج.م")

    # إضافة قسم الخدمات الإضافية (كله بالجنيه المصري)
    with st.container(border=True):
        st.subheader("🔧 الخدمات الإضافية (بالجنيه المصري)")

        services_col1, services_col2, services_col3 = st.columns(3)

        with services_col1:
            st.markdown("**خدمات التنظيف والصيانة**")
            polishing_egp = st.number_input("تلميع وتنظيف (ج.م)", key="polish_egp", min_value=0.0, format="%.2f")
            repair_egp = st.number_input("إصلاح وصيانة (ج.م)", key="repair_egp", min_value=0.0, format="%.2f")

        with services_col2:
            st.markdown("**خدمات التخصيص والتغليف**")
            engraving_egp = st.number_input("نقش وحفر (ج.م)", key="engrave_egp", min_value=0.0, format="%.2f")
            packaging_egp = st.number_input("تغليف خاص (ج.م)", key="package_egp", min_value=0.0, format="%.2f")

        with services_col3:
            st.markdown("**إجمالي الخدمات**")
            services_egp_total = polishing_egp + repair_egp + engraving_egp + packaging_egp
            st.metric("إجمالي الخدمات", f"{services_egp_total:.2f} ج.م")

            # تفاصيل الخدمات
            if services_egp_total > 0:
                st.caption(f"تنظيف: {polishing_egp + repair_egp:.2f} ج.م")
                st.caption(f"تخصيص: {engraving_egp + packaging_egp:.2f} ج.م")

    st.write("---")
    st.header("💵 الملخص النهائي للحساب")

    total_gold_change = -(gold_weight_usd + gold_weight_egp)
    final_usd_charge = workmanship_subtotal_usd + workshop_stones_total
    final_egp_charge = workmanship_subtotal_egp + client_stones_total + services_egp_total

    # عرض الملخص المفصل
    st.subheader("📊 تفاصيل الحساب")

    # الصف الأول - المواد الأساسية
    detail_col1, detail_col2, detail_col3, detail_col4 = st.columns(4)
    with detail_col1:
        st.metric("الذهب المستخدم", f"{abs(total_gold_change):.2f} جرام")
    with detail_col2:
        st.metric("مصنعية ($)", f"$ {workmanship_subtotal_usd:.2f}")
    with detail_col3:
        st.metric("مصنعية (ج.م)", f"{workmanship_subtotal_egp:.2f} ج.م")
    with detail_col4:
        st.metric("أحجار الورشة", f"$ {workshop_stones_total:.2f}")

    # الصف الثاني - الخدمات والإضافات
    detail2_col1, detail2_col2, detail2_col3, detail2_col4 = st.columns(4)
    with detail2_col1:
        st.metric("تركيب أحجار العميل", f"{client_stones_total:.2f} ج.م")
    with detail2_col2:
        st.metric("خدمات إضافية (ج.م)", f"{services_egp_total:.2f} ج.م")
    with detail2_col3:
        # عرض تفاصيل الخدمات
        if services_egp_total > 0:
            st.metric("تفاصيل الخدمات", f"{services_egp_total:.2f} ج.م")
        else:
            st.metric("خدمات إضافية", "لا توجد")
    with detail2_col4:
        total_stones_count = workshop_stone_count + client_stone_count
        st.metric("إجمالي الأحجار", f"{total_stones_count} حجر")

    st.write("---")

    # الإجمالي النهائي
    st.subheader("💰 الإجمالي النهائي")
    final_col1, final_col2, final_col3 = st.columns(3)
    with final_col1:
        st.metric(
            "🥇 إجمالي بالدولار",
            f"$ {final_usd_charge:.2f}",
            delta="مصنعية + أحجار + خدمات"
        )
    with final_col2:
        st.metric(
            "💵 إجمالي بالجنيه",
            f"{final_egp_charge:.2f} ج.م",
            delta="مصنعية + تركيب + خدمات"
        )
    with final_col3:
        # حساب تقديري بالجنيه (للعرض فقط)
        if exchange_rate > 0:
            estimated_total_egp = (
                final_egp_charge + (final_usd_charge * exchange_rate)
            )
        else:
            estimated_total_egp = 0
        if exchange_rate > 0:
            st.metric("📊 تقدير إجمالي (ج.م)", f"{estimated_total_egp:.2f} ج.م",
                      delta=f"بسعر صرف {exchange_rate:.2f}")
        else:
            st.metric(
                "📊 تقدير إجمالي",
                "غير محدد",
                delta="أدخل سعر الصرف"
            )

    if st.button("💾 حفظ الفاتورة", type="primary"):
        if not customer_name.strip():
            st.error("❌ خطأ: الرجاء إدخال اسم العميل.")
        elif total_gold_change == 0 and final_usd_charge == 0 and final_egp_charge == 0:
            st.error("❌ خطأ: الرجاء إدخال بيانات الفاتورة.")
        else:
            success = save_transaction(
                customer=customer_name.strip(),
                date=str(invoice_date),
                desc=invoice_description or "فاتورة جديدة",
                gold=total_gold_change,
                usd=final_usd_charge,
                egp=final_egp_charge
            )
            if success:
                st.success(f"✅ تم حفظ الفاتورة بنجاح للعميل: {customer_name}")
                st.balloons()


def customer_accounts_page():
    """
    صفحة حسابات العملاء المحدثة (مع نظام الدفعات والأرصدة).
    """
    st.header("👥 حسابات العملاء")

    if not os.path.isfile(FILE_PATH):
        st.warning("⚠️ لم يتم تسجيل أي فواتير بعد.")
        return

    try:
        # استخدام التخزين المؤقت لتحسين الأداء
        df = load_data_cached()
        customer_list = get_customer_list()
        
        if not customer_list:
            st.warning("⚠️ لا توجد عملاء مسجلين.")
            return
            
    except Exception as e:
        st.error(f"❌ خطأ في قراءة البيانات: {e}")
        return
    
    selected_customer = st.selectbox("🔍 اختر العميل لعرض حسابه:", customer_list)
    
    if selected_customer:
        # --- قسم إضافة حركة جديدة (رصيد، دفعة، تسوية) ---
        with st.expander("➕ إضافة حركة جديدة لحساب العميل"):
            with st.form("transaction_form", clear_on_submit=True):
                st.write("**تسجيل رصيد افتتاحي أو دفعة أو تسوية**")
                t_type = st.selectbox("نوع الحركة:", ["دفعة من العميل", "رصيد افتتاحي", "تسوية / خصم"])
                t_date = st.date_input("تاريخ الحركة", value=datetime.now().date())
                t_desc = st.text_input("بيان الحركة", placeholder="مثال: دفعة كاش تحت الحساب")
                
                c1, c2, c3 = st.columns(3)
                with c1:
                    t_gold = st.number_input("ذهب (جرام)", format="%.2f", min_value=0.0)
                with c2:
                    t_usd = st.number_input("دولار ($)", format="%.2f", min_value=0.0)
                with c3:
                    t_egp = st.number_input("جنيه (EGP)", format="%.2f", min_value=0.0)
                
                submitted = st.form_submit_button("💾 حفظ الحركة", type="primary")
                if submitted:
                    if t_gold == 0 and t_usd == 0 and t_egp == 0:
                        st.error("❌ يجب إدخال قيمة واحدة على الأقل")
                    else:
                        # تحديد إشارة القيم حسب نوع الحركة
                        if t_type in ["دفعة من العميل", "تسوية / خصم"]:
                            t_gold, t_usd, t_egp = -abs(t_gold), -abs(t_usd), -abs(t_egp)
                        else:  # رصيد افتتاحي
                            t_gold, t_usd, t_egp = abs(t_gold), abs(t_usd), abs(t_egp)

                        success = save_transaction(
                            selected_customer, 
                            str(t_date), 
                            t_desc or t_type, 
                            t_gold, 
                            t_usd, 
                            t_egp
                        )
                        if success:
                            st.success(f"✅ تم حفظ الحركة '{t_type}' لحساب {selected_customer}")
                            st.rerun()

        # --- قسم عرض الحساب ---
        st.write("---")
        st.header(f"📊 كشف حساب: {selected_customer}")
        
        try:
            df = pd.read_csv(FILE_PATH, encoding='utf-8-sig')  # إعادة قراءة الملف لضمان تحديث البيانات
            customer_df = df[df['customer_name'] == selected_customer].copy()
            
            if not customer_df.empty:
                total_gold = customer_df['gold_change'].sum()
                total_usd = customer_df['usd_change'].sum()
                total_egp = customer_df['egp_change'].sum()
                
                # عرض الأرصدة
                balance_col1, balance_col2, balance_col3 = st.columns(3)
                with balance_col1:
                    color = "normal" if total_gold >= 0 else "inverse"
                    st.metric("رصيد الذهب النهائي", f"{total_gold:.2f} جرام", delta_color=color)
                with balance_col2:
                    color = "normal" if total_usd >= 0 else "inverse"
                    st.metric("رصيد الدولار النهائي", f"$ {total_usd:.2f}", delta_color=color)
                with balance_col3:
                    color = "normal" if total_egp >= 0 else "inverse"
                    st.metric("رصيد الجنيه النهائي", f"{total_egp:.2f} ج.م", delta_color=color)
                
                st.write("---")
                st.subheader("📋 بيان العمليات")
                
                # تحسين عرض البيانات
                display_df = customer_df.copy()
                try:
                    # تحويل التاريخ وترتيب البيانات
                    date_col = pd.to_datetime(display_df['date'])
                    display_df = display_df.iloc[date_col.argsort()[::-1]]
                    display_df['date'] = date_col.dt.strftime('%Y-%m-%d')
                except Exception:
                    # في حالة فشل التحويل، استخدم الترتيب النصي
                    display_df = display_df.iloc[
                        display_df['date'].argsort()[::-1]
                    ]
                
                columns_to_show = [
                    'date', 'description', 'gold_change',
                    'usd_change', 'egp_change'
                ]

                st.dataframe(
                    display_df[columns_to_show],
                    use_container_width=True,
                    column_config={
                        "date": "التاريخ",
                        "description": "البيان",
                        "gold_change": st.column_config.NumberColumn(
                            "الذهب (جرام)", format="%.2f"
                        ),
                        "usd_change": st.column_config.NumberColumn(
                            "الدولار ($)", format="%.2f"
                        ),
                        "egp_change": st.column_config.NumberColumn(
                            "الجنيه (ج.م)", format="%.2f"
                        )
                    }
                )
            else:
                st.info("لا توجد عمليات لهذا العميل.")
                
        except Exception as e:
            st.error(f"❌ خطأ في عرض البيانات: {e}")


def view_invoices_page():
    """صفحة عرض جميع الفواتير"""
    st.header("📊 عرض الفواتير المحفوظة")
    
    if not os.path.isfile(FILE_PATH):
        st.info("📝 لا توجد فواتير محفوظة لعرضها.")
        return
        
    try:
        df = pd.read_csv(FILE_PATH, encoding='utf-8-sig')
        
        if df.empty:
            st.info("📝 لا توجد فواتير محفوظة لعرضها.")
            return
            
        # إضافة فلاتر
        st.subheader("🔍 فلترة البيانات")
        filter_col1, filter_col2 = st.columns(2)
        
        with filter_col1:
            customers = ['الكل'] + list(df['customer_name'].unique())
            selected_customer_filter = st.selectbox("فلترة حسب العميل:", customers)
            
        with filter_col2:
            date_range = st.date_input("فلترة حسب التاريخ:", value=[], help="اختر تاريخ البداية والنهاية")
        
        # تطبيق الفلاتر
        filtered_df = df.copy()
        
        if selected_customer_filter != 'الكل':
            filtered_df = filtered_df[filtered_df['customer_name'] == selected_customer_filter]
            
        if len(date_range) == 2:
            filtered_df['date'] = pd.to_datetime(filtered_df['date'])
            filtered_df = filtered_df[
                (filtered_df['date'] >= pd.to_datetime(date_range[0])) & 
                (filtered_df['date'] <= pd.to_datetime(date_range[1]))
            ]
        
        st.write("---")
        st.subheader(f"📋 النتائج ({len(filtered_df)} عملية)")
        
        if not filtered_df.empty:
            # تحسين عرض البيانات
            display_df = filtered_df.copy()
            display_df['date'] = pd.to_datetime(display_df['date']).dt.strftime('%Y-%m-%d')
            display_df = display_df.sort_values(by='date', ascending=False)
            
            st.dataframe(
                display_df,
                use_container_width=True,
                column_config={
                    "customer_name": "اسم العميل",
                    "date": "التاريخ",
                    "description": "البيان",
                    "gold_change": st.column_config.NumberColumn("الذهب (جرام)", format="%.2f"),
                    "usd_change": st.column_config.NumberColumn("الدولار ($)", format="%.2f"),
                    "egp_change": st.column_config.NumberColumn("الجنيه (ج.م)", format="%.2f")
                }
            )
            
            # إحصائيات سريعة
            st.write("---")
            st.subheader("📈 إحصائيات سريعة")
            stats_col1, stats_col2, stats_col3, stats_col4 = st.columns(4)
            
            with stats_col1:
                st.metric("عدد العمليات", len(filtered_df))
            with stats_col2:
                st.metric("إجمالي الذهب", f"{filtered_df['gold_change'].sum():.2f} جرام")
            with stats_col3:
                st.metric("إجمالي الدولار", f"$ {filtered_df['usd_change'].sum():.2f}")
            with stats_col4:
                st.metric("إجمالي الجنيه", f"{filtered_df['egp_change'].sum():.2f} ج.م")
        else:
            st.info("لا توجد نتائج تطابق الفلاتر المحددة.")
            
    except Exception as e:
        st.error(f"❌ خطأ في قراءة البيانات: {e}")


def reports_page():
    """صفحة التقارير والإحصائيات"""
    st.header("📈 إحصائيات وتقارير")
    
    if not os.path.isfile(FILE_PATH):
        st.info("📝 لا توجد بيانات لإنشاء التقارير.")
        return
        
    try:
        df = pd.read_csv(FILE_PATH, encoding='utf-8-sig')
        
        if df.empty:
            st.info("📝 لا توجد بيانات لإنشاء التقارير.")
            return
            
        # تقرير العملاء
        st.subheader("👥 تقرير العملاء")
        customer_summary = df.groupby('customer_name').agg({
            'gold_change': 'sum',
            'usd_change': 'sum',
            'egp_change': 'sum'
        }).round(2)
        
        st.dataframe(
            customer_summary,
            use_container_width=True,
            column_config={
                "gold_change": st.column_config.NumberColumn("رصيد الذهب (جرام)", format="%.2f"),
                "usd_change": st.column_config.NumberColumn("رصيد الدولار ($)", format="%.2f"),
                "egp_change": st.column_config.NumberColumn("رصيد الجنيه (ج.م)", format="%.2f")
            }
        )
        
        # إحصائيات عامة
        st.write("---")
        st.subheader("📊 إحصائيات عامة")
        
        general_col1, general_col2, general_col3, general_col4 = st.columns(4)
        
        with general_col1:
            st.metric("عدد العملاء", df['customer_name'].nunique())
        with general_col2:
            st.metric("إجمالي العمليات", len(df))
        with general_col3:
            st.metric("إجمالي الذهب المتداول", f"{abs(df['gold_change']).sum():.2f} جرام")
        with general_col4:
            st.metric("إجمالي المبالغ بالدولار", f"$ {abs(df['usd_change']).sum():.2f}")
            
    except Exception as e:
        st.error(f"❌ خطأ في إنشاء التقارير: {e}")


def settings_page():
    """صفحة الإعدادات"""
    st.header("⚙️ الإعدادات")
    
    st.subheader("📁 إدارة البيانات")
    
    # نسخ احتياطي
    if st.button("💾 إنشاء نسخة احتياطية"):
        if os.path.isfile(FILE_PATH):
            try:
                backup_name = f"backup_invoices_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df = pd.read_csv(FILE_PATH, encoding='utf-8-sig')
                df.to_csv(backup_name, index=False, encoding='utf-8-sig')
                st.success(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            except Exception as e:
                st.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        else:
            st.warning("⚠️ لا توجد بيانات لعمل نسخة احتياطية منها.")
    
    # معلومات النظام
    st.write("---")
    st.subheader("ℹ️ معلومات النظام")
    
    if os.path.isfile(FILE_PATH):
        try:
            df = pd.read_csv(FILE_PATH, encoding='utf-8-sig')
            file_size = os.path.getsize(FILE_PATH)
            
            info_col1, info_col2, info_col3 = st.columns(3)
            with info_col1:
                st.metric("عدد السجلات", len(df))
            with info_col2:
                st.metric("حجم الملف", f"{file_size / 1024:.1f} KB")
            with info_col3:
                st.metric("عدد العملاء", df['customer_name'].nunique())
                
        except Exception as e:
            st.error(f"❌ خطأ في قراءة معلومات النظام: {e}")
    else:
        st.info("📝 لا توجد بيانات في النظام.")

# =================================================================================
# 3. بناء الواجهة الرئيسية والتنقل بين الصفحات
# =================================================================================

# إعداد الشريط الجانبي
st.sidebar.title("💎 Crestal Diamond")

# محاولة عرض الشعار (اختياري)
try:
    st.sidebar.image("assets/logo.png", use_container_width=True)
except:
    pass  # تجاهل الخطأ إذا لم يكن الشعار موجود

# قائمة الصفحات
PAGES = {
    "📄 إنشاء فاتورة جديدة": create_invoice_page,
    "👥 حسابات العملاء": customer_accounts_page,
    "📊 عرض الفواتير المحفوظة": view_invoices_page,
    "📈 إحصائيات وتقارير": reports_page,
    "⚙️ الإعدادات": settings_page,
}

# التنقل بين الصفحات
selection = st.sidebar.radio("🧭 اذهب إلى", list(PAGES.keys()))

# تشغيل الصفحة المحددة
try:
    page = PAGES[selection]
    page()
except Exception as e:
    st.error(f"❌ خطأ في تحميل الصفحة: {e}")
    st.info("يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.")

# إضافة معلومات في الشريط الجانبي
st.sidebar.write("---")
st.sidebar.info("💡 **نصائح الاستخدام:**\n\n• استخدم صفحة إنشاء الفاتورة لتسجيل المبيعات\n• استخدم صفحة حسابات العملاء لإدارة الدفعات\n• راجع التقارير لمتابعة الأداء")

# معلومات الإصدار
st.sidebar.caption("🔧 الإصدار 2.0 - محدث ومحسن")
