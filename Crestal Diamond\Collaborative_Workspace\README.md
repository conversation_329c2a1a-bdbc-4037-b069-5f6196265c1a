# مجلد العمل التعاوني - Collaborative Workspace

## نظرة عامة
هذا المجلد مخصص للتنسيق والتعاون بين الوكلاء الثلاثة في تطوير مشروع Crestal Diamond:
- **Augment Agent**: وكيل التطوير الرئيسي
- **Gemini CLI**: محلل الكود المتقدم  
- **Memory Agent**: وكيل إدارة المعرفة

## هيكل المجلد

### الملفات الأساسية
- `README.md`: هذا الملف - دليل المجلد
- `project_paths.json`: جميع المسارات المهمة في المشروع
- `shared_memory.md`: الذاكرة المشتركة للفريق
- `collaboration_log.md`: سجل جميع أنشطة التعاون
- `task_management.md`: إدارة المهام وتوزيعها
- `memory_agent_config.md`: إعدادات وكيل الذاكرة

### ملفات التشغيل
- `gemini_commands.bat`: أوامر استدعاء Gemini CLI
- `run_analysis.bat`: تشغيل التحليل الشامل
- `backup_project.bat`: نسخ احتياطي للمشروع

## كيفية الاستخدام

### للمطورين
1. **قراءة الحالة الحالية**: ابدأ بقراءة `shared_memory.md`
2. **مراجعة المهام**: تحقق من `task_management.md`
3. **تحديث التقدم**: سجل عملك في `collaboration_log.md`
4. **استخدام المسارات**: ارجع لـ `project_paths.json` للمسارات

### لـ Augment Agent
```markdown
1. فحص الملفات الجديدة
2. تحديث shared_memory.md
3. إضافة المهام الجديدة في task_management.md
4. تسجيل التقدم في collaboration_log.md
```

### لـ Gemini CLI
```bash
# الانتقال لمجلد المشروع
cd "C:\Users\<USER>\Crestal Diamond"

# تشغيل التحليل
gemini analyze invoice_app.py
gemini analyze pages/

# تشغيل الاختبارات
gemini test streamlit run invoice_app.py

# فحص الأمان
gemini security-check .
```

### لـ Memory Agent
```markdown
1. مراقبة جميع التغييرات
2. تحديث قاعدة المعرفة
3. إنشاء التقارير اليومية
4. حفظ السياق التاريخي
```

## قواعد التعاون

### 1. التواصل
- **جميع التحديثات** يجب تسجيلها في الملفات المشتركة
- **لا تعديل مباشر** على ملفات المشروع الأصلية بدون تنسيق
- **التوثيق إجباري** لجميع التغييرات المهمة

### 2. تنظيم العمل
- **مهمة واحدة لكل وكيل** في نفس الوقت
- **تحديث الحالة** كل 4 ساعات على الأقل
- **مراجعة جماعية** للقرارات المهمة

### 3. إدارة الملفات
- **نسخ احتياطي** قبل أي تعديل مهم
- **تسمية واضحة** لجميع الملفات الجديدة
- **ترميز UTF-8** لجميع الملفات

## الأوامر المهمة

### تشغيل المشروع
```bash
cd "C:\Users\<USER>\Crestal Diamond"
streamlit run invoice_app.py
```

### استدعاء Gemini CLI
```bash
cd "C:\Users\<USER>\Users\mo_as\Crestal Diamond" "C:\Users\<USER>\Backup\Crestal Diamond" /E /I /Y
```

## حالة المشروع الحالية

### ✅ مكتمل
- إعداد مجلد العمل التعاوني
- فحص الملفات الأساسية
- إنشاء نظام التوثيق

### 🔄 قيد العمل
- تحليل جميع ملفات المشروع
- إصلاح الأخطاء المكتشفة
- تحسين الأمان

### ⏳ مخطط
- اختبار شامل للتطبيق
- إضافة ميزات جديدة
- إنشاء دليل المستخدم

## جهات الاتصال والدعم

### في حالة المشاكل التقنية
1. تحقق من `collaboration_log.md` للمشاكل المشابهة
2. راجع `shared_memory.md` للحلول المعروفة
3. استشر Memory Agent للسياق التاريخي

### للتطوير والتحسين
1. أضف اقتراحك في `task_management.md`
2. ناقش مع الفريق في `collaboration_log.md`
3. وثق القرار في `shared_memory.md`

---
**آخر تحديث**: 2025-07-10
**المسؤول**: Augment Agent
**الحالة**: نشط ومتاح للاستخدام
