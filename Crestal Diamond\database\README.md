# 🗄️ قاعدة البيانات - نظام كريستال دايموند

## 📋 نظرة عامة

تم تطوير نظام قاعدة البيانات لنظام كريستال دايموند لاستخدام MySQL بدلاً من ملفات CSV، مما يوفر:

- ✅ أداء أفضل وسرعة في الاستعلامات
- ✅ موثوقية أعلى وحماية البيانات
- ✅ دعم المعاملات المتزامنة
- ✅ إمكانيات بحث متقدمة
- ✅ نسخ احتياطية تلقائية

## 🏗️ هيكل قاعدة البيانات

### الجداول الرئيسية:

#### 1. جدول العملاء (customers)
```sql
- id: المعرف الفريد
- customer_name: اسم العميل
- phone: رقم الهاتف
- address: العنوان
- email: البريد الإلكتروني
- notes: ملاحظات
- total_balance: الرصيد الإجمالي
- created_at: تاريخ الإنشاء
- updated_at: تاريخ التحديث
```

#### 2. جدول الفواتير (invoices)
```sql
- id: المعرف الفريد
- invoice_number: رقم الفاتورة
- customer_id: معرف العميل
- تفاصيل المنتج الرئيسي
- تفاصيل الأحجار (ورشة وعميل)
- الخدمات الإضافية
- المجاميع والخصومات
- معلومات الدفع
- التواريخ والحالة
```

#### 3. جدول المخزون (inventory)
```sql
- id: المعرف الفريد
- item_name: اسم العنصر
- category: الفئة
- description: الوصف
- quantity: الكمية
- unit_price_usd/egp: سعر الوحدة
- total_value_usd/egp: القيمة الإجمالية
- supplier: المورد
- location: الموقع
- minimum_stock: الحد الأدنى للمخزون
```

#### 4. جدول المعاملات (transactions)
```sql
- id: المعرف الفريد
- customer_id: معرف العميل
- invoice_id: معرف الفاتورة
- transaction_type: نوع المعاملة
- amount_usd/egp: المبلغ
- payment_method: طريقة الدفع
- transaction_date: تاريخ المعاملة
- description: الوصف
```

## 📁 ملفات النظام

### 1. `database_config.py`
- إعداد الاتصال بقاعدة البيانات
- إدارة الاتصالات
- تنفيذ الاستعلامات

### 2. `create_tables.py`
- إنشاء جميع الجداول
- تعريف الفهارس والعلاقات
- إعداد قاعدة البيانات الأولي

### 3. `database_operations.py`
- عمليات العملاء
- عمليات الفواتير
- عمليات المخزون
- عمليات المعاملات المالية

### 4. `migrate_csv_to_mysql.py`
- تهجير البيانات من CSV إلى MySQL
- تحويل التنسيقات
- التحقق من صحة البيانات

### 5. `test_database.py`
- اختبار الاتصال
- عرض البيانات
- إضافة بيانات تجريبية

## ⚙️ إعداد قاعدة البيانات

### المتطلبات:
```bash
pip install mysql-connector-python
```

### إعدادات الاتصال:
```python
HOST = "localhost"
DATABASE = "crestal_diamond"
USER = "root"
PASSWORD = "2452329511"
PORT = 3306
```

## 🚀 التشغيل

### 1. إنشاء قاعدة البيانات والجداول:
```python
python database/create_tables.py
```

### 2. تهجير البيانات من CSV:
```python
python database/migrate_csv_to_mysql.py
```

### 3. اختبار النظام:
```python
python database/test_database.py
```

## 🔧 الاستخدام في التطبيق

### استيراد العمليات:
```python
from database.database_operations import (
    customer_ops, 
    invoice_ops, 
    inventory_ops, 
    transaction_ops
)
```

### أمثلة الاستخدام:

#### إضافة عميل جديد:
```python
customer_data = {
    'customer_name': 'أحمد محمد',
    'phone': '01234567890',
    'address': 'القاهرة',
    'email': '<EMAIL>'
}
customer_ops.add_customer(customer_data)
```

#### الحصول على جميع العملاء:
```python
customers_df = customer_ops.get_all_customers()
```

#### إضافة فاتورة جديدة:
```python
invoice_data = {
    'invoice_number': 'INV-001',
    'customer_name': 'أحمد محمد',
    'total_usd': 1000.00,
    'total_egp': 30000.00
    # ... باقي البيانات
}
invoice_ops.add_invoice(invoice_data)
```

## 🔒 الأمان

- استخدام Prepared Statements لمنع SQL Injection
- تشفير كلمات المرور
- صلاحيات محدودة للمستخدمين
- نسخ احتياطية منتظمة

## 📊 المراقبة والصيانة

- مراقبة أداء الاستعلامات
- تحسين الفهارس
- تنظيف البيانات القديمة
- نسخ احتياطية يومية

## 🆘 استكشاف الأخطاء

### مشاكل الاتصال:
1. تأكد من تشغيل MySQL Server
2. تحقق من صحة بيانات الاتصال
3. تأكد من وجود قاعدة البيانات

### مشاكل الأداء:
1. تحقق من الفهارس
2. راجع الاستعلامات المعقدة
3. راقب استخدام الذاكرة

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تم التطوير بواسطة فريق كريستال دايموند** 💎
