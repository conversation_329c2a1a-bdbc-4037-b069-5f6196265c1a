"""
🧪 اختبار شامل لجميع وظائف النظام - نظام كريستال دايموند
Comprehensive System Testing for Crestal Diamond System
"""

from database_operations import customer_ops, invoice_ops, inventory_ops, transaction_ops
from database_config import test_connection
import pandas as pd
import logging
from datetime import datetime, date

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    logger.info("🔌 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        if test_connection():
            logger.info("✅ الاتصال بقاعدة البيانات ناجح")
            return True
        else:
            logger.error("❌ فشل في الاتصال بقاعدة البيانات")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصال: {str(e)}")
        return False

def test_customer_operations():
    """اختبار عمليات العملاء"""
    logger.info("👥 اختبار عمليات العملاء...")
    
    try:
        # جلب جميع العملاء
        customers_df = customer_ops.get_all_customers()
        logger.info(f"📊 تم جلب {len(customers_df)} عميل من قاعدة البيانات")
        
        if not customers_df.empty:
            # اختبار جلب عميل محدد
            first_customer = customers_df.iloc[0]['customer_name']
            customer_details = customer_ops.get_customer_by_name(first_customer)
            
            if customer_details is not None:
                logger.info(f"✅ تم جلب تفاصيل العميل: {first_customer}")
            else:
                logger.warning(f"⚠️ لم يتم العثور على تفاصيل العميل: {first_customer}")
            
            # عرض عينة من العملاء
            sample_customers = customers_df['customer_name'].head(5).tolist()
            logger.info(f"📋 عينة من العملاء: {sample_customers}")
            
            return True
        else:
            logger.warning("⚠️ لا توجد عملاء في قاعدة البيانات")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات العملاء: {str(e)}")
        return False

def test_invoice_operations():
    """اختبار عمليات الفواتير"""
    logger.info("🧾 اختبار عمليات الفواتير...")
    
    try:
        # جلب جميع الفواتير
        invoices_df = invoice_ops.get_all_invoices()
        logger.info(f"📊 تم جلب {len(invoices_df)} فاتورة من قاعدة البيانات")
        
        if not invoices_df.empty:
            # حساب الإحصائيات
            total_sales_usd = invoices_df['total_usd'].sum()
            total_sales_egp = invoices_df['total_egp'].sum()
            avg_invoice_usd = invoices_df['total_usd'].mean()
            
            logger.info(f"💰 إجمالي المبيعات: ${total_sales_usd:,.2f} USD | {total_sales_egp:,.2f} EGP")
            logger.info(f"📈 متوسط قيمة الفاتورة: ${avg_invoice_usd:.2f} USD")
            
            # عرض عينة من الفواتير
            sample_invoices = invoices_df['invoice_number'].head(5).tolist()
            logger.info(f"📋 عينة من الفواتير: {sample_invoices}")
            
            # اختبار حالات الفواتير
            status_counts = invoices_df['status'].value_counts()
            logger.info(f"📊 حالات الفواتير: {status_counts.to_dict()}")
            
            return True
        else:
            logger.warning("⚠️ لا توجد فواتير في قاعدة البيانات")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات الفواتير: {str(e)}")
        return False

def test_inventory_operations():
    """اختبار عمليات المخزون"""
    logger.info("📦 اختبار عمليات المخزون...")
    
    try:
        # جلب جميع عناصر المخزون
        inventory_df = inventory_ops.get_all_inventory()
        logger.info(f"📊 تم جلب {len(inventory_df)} عنصر من المخزون")
        
        if not inventory_df.empty:
            # حساب الإحصائيات
            total_value_usd = inventory_df['total_value_usd'].sum()
            total_value_egp = inventory_df['total_value_egp'].sum()
            total_quantity = inventory_df['quantity'].sum()
            
            logger.info(f"💰 إجمالي قيمة المخزون: ${total_value_usd:,.2f} USD | {total_value_egp:,.2f} EGP")
            logger.info(f"📦 إجمالي الكمية: {total_quantity} قطعة")
            
            # عرض الفئات
            categories = inventory_df['category'].value_counts()
            logger.info(f"📊 فئات المخزون: {categories.to_dict()}")
            
            # عرض عينة من المخزون
            sample_items = inventory_df['item_name'].head(5).tolist()
            logger.info(f"📋 عينة من المخزون: {sample_items}")
            
            # فحص المخزون المنخفض
            low_stock = inventory_df[inventory_df['quantity'] <= inventory_df['minimum_stock']]
            if not low_stock.empty:
                logger.warning(f"⚠️ تحذير: {len(low_stock)} عنصر بمخزون منخفض")
                low_stock_items = low_stock['item_name'].tolist()
                logger.warning(f"📉 العناصر المنخفضة: {low_stock_items}")
            else:
                logger.info("✅ جميع عناصر المخزون في المستوى الطبيعي")
            
            return True
        else:
            logger.warning("⚠️ لا توجد عناصر في المخزون")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات المخزون: {str(e)}")
        return False

def test_transaction_operations():
    """اختبار عمليات المعاملات"""
    logger.info("💳 اختبار عمليات المعاملات...")
    
    try:
        # جلب جميع المعاملات
        transactions_df = transaction_ops.get_all_transactions()
        logger.info(f"📊 تم جلب {len(transactions_df)} معاملة من قاعدة البيانات")
        
        if not transactions_df.empty:
            # حساب الإحصائيات
            total_amount_usd = transactions_df['amount_usd'].sum()
            total_amount_egp = transactions_df['amount_egp'].sum()
            
            logger.info(f"💰 إجمالي المعاملات: ${total_amount_usd:,.2f} USD | {total_amount_egp:,.2f} EGP")
            
            # تحليل أنواع المعاملات
            transaction_types = transactions_df['transaction_type'].value_counts()
            logger.info(f"📊 أنواع المعاملات: {transaction_types.to_dict()}")
            
            # اختبار معاملات عميل محدد
            if not transactions_df.empty and 'customer_name' in transactions_df.columns:
                first_customer = transactions_df.iloc[0]['customer_name']
                customer_transactions = transaction_ops.get_customer_transactions(first_customer)
                logger.info(f"👤 معاملات العميل '{first_customer}': {len(customer_transactions)} معاملة")
            else:
                logger.warning("⚠️ لا توجد أعمدة customer_name في المعاملات")
            
            return True
        else:
            logger.warning("⚠️ لا توجد معاملات في قاعدة البيانات")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات المعاملات: {str(e)}")
        return False

def test_data_integrity():
    """اختبار تكامل البيانات"""
    logger.info("🔍 اختبار تكامل البيانات...")
    
    try:
        # جلب البيانات
        customers_df = customer_ops.get_all_customers()
        invoices_df = invoice_ops.get_all_invoices()
        transactions_df = transaction_ops.get_all_transactions()
        
        # فحص تطابق أسماء العملاء
        invoice_customers = set(invoices_df['customer_name'].dropna()) if 'customer_name' in invoices_df.columns else set()
        transaction_customers = set(transactions_df['customer_name'].dropna()) if 'customer_name' in transactions_df.columns else set()
        registered_customers = set(customers_df['customer_name'].dropna()) if 'customer_name' in customers_df.columns else set()
        
        # العملاء في الفواتير ولكن غير مسجلين
        unregistered_invoice_customers = invoice_customers - registered_customers
        if unregistered_invoice_customers:
            logger.warning(f"⚠️ عملاء في الفواتير غير مسجلين: {unregistered_invoice_customers}")
        else:
            logger.info("✅ جميع عملاء الفواتير مسجلون")
        
        # العملاء في المعاملات ولكن غير مسجلين
        unregistered_transaction_customers = transaction_customers - registered_customers
        if unregistered_transaction_customers:
            logger.warning(f"⚠️ عملاء في المعاملات غير مسجلين: {unregistered_transaction_customers}")
        else:
            logger.info("✅ جميع عملاء المعاملات مسجلون")
        
        # فحص الأرقام السالبة في الأسعار
        negative_prices = invoices_df[
            (invoices_df['total_usd'] < 0) | 
            (invoices_df['total_egp'] < 0)
        ]
        
        if not negative_prices.empty:
            logger.warning(f"⚠️ فواتير بأسعار سالبة: {len(negative_prices)}")
        else:
            logger.info("✅ جميع أسعار الفواتير صحيحة")
        
        # فحص التواريخ المستقبلية
        future_invoices = invoices_df[invoices_df['invoice_date'] > date.today()]
        if not future_invoices.empty:
            logger.warning(f"⚠️ فواتير بتواريخ مستقبلية: {len(future_invoices)}")
        else:
            logger.info("✅ جميع تواريخ الفواتير صحيحة")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار تكامل البيانات: {str(e)}")
        return False

def test_performance():
    """اختبار الأداء"""
    logger.info("⚡ اختبار الأداء...")
    
    try:
        import time
        
        # اختبار سرعة جلب البيانات
        start_time = time.time()
        customers_df = customer_ops.get_all_customers()
        customer_time = time.time() - start_time
        
        start_time = time.time()
        invoices_df = invoice_ops.get_all_invoices()
        invoice_time = time.time() - start_time
        
        start_time = time.time()
        inventory_df = inventory_ops.get_all_inventory()
        inventory_time = time.time() - start_time
        
        start_time = time.time()
        transactions_df = transaction_ops.get_all_transactions()
        transaction_time = time.time() - start_time
        
        logger.info(f"⏱️ أوقات الاستعلام:")
        logger.info(f"   👥 العملاء: {customer_time:.3f} ثانية ({len(customers_df)} سجل)")
        logger.info(f"   🧾 الفواتير: {invoice_time:.3f} ثانية ({len(invoices_df)} سجل)")
        logger.info(f"   📦 المخزون: {inventory_time:.3f} ثانية ({len(inventory_df)} سجل)")
        logger.info(f"   💳 المعاملات: {transaction_time:.3f} ثانية ({len(transactions_df)} سجل)")
        
        total_time = customer_time + invoice_time + inventory_time + transaction_time
        logger.info(f"⏱️ إجمالي الوقت: {total_time:.3f} ثانية")
        
        if total_time < 2.0:
            logger.info("🚀 الأداء ممتاز!")
        elif total_time < 5.0:
            logger.info("✅ الأداء جيد")
        else:
            logger.warning("⚠️ الأداء يحتاج تحسين")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الأداء: {str(e)}")
        return False

def main():
    """تشغيل الاختبار الشامل"""
    logger.info("🚀 بدء الاختبار الشامل لنظام كريستال دايموند...")
    logger.info("=" * 70)
    
    tests = [
        ("اختبار الاتصال", test_database_connection),
        ("اختبار العملاء", test_customer_operations),
        ("اختبار الفواتير", test_invoice_operations),
        ("اختبار المخزون", test_inventory_operations),
        ("اختبار المعاملات", test_transaction_operations),
        ("اختبار تكامل البيانات", test_data_integrity),
        ("اختبار الأداء", test_performance)
    ]
    
    passed_tests = 0
    failed_tests = 0
    
    for test_name, test_function in tests:
        logger.info(f"\n🧪 {test_name}...")
        try:
            if test_function():
                passed_tests += 1
                logger.info(f"✅ {test_name}: نجح")
            else:
                failed_tests += 1
                logger.error(f"❌ {test_name}: فشل")
        except Exception as e:
            failed_tests += 1
            logger.error(f"❌ {test_name}: خطأ - {str(e)}")
    
    # تقرير النتائج النهائي
    logger.info("=" * 70)
    logger.info("📊 تقرير الاختبار الشامل:")
    logger.info(f"   ✅ نجح: {passed_tests}/{len(tests)} اختبار")
    logger.info(f"   ❌ فشل: {failed_tests}/{len(tests)} اختبار")
    logger.info(f"   📈 معدل النجاح: {(passed_tests/len(tests)*100):.1f}%")
    
    if failed_tests == 0:
        logger.info("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي!")
        return True
    else:
        logger.warning(f"⚠️ {failed_tests} اختبار فشل. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🏁 انتهى الاختبار الشامل بنجاح!")
        print("💡 النظام جاهز للاستخدام الكامل")
    else:
        print("\n🚨 انتهى الاختبار مع وجود مشاكل!")
        print("🔧 يرجى إصلاح المشاكل المذكورة أعلاه")
