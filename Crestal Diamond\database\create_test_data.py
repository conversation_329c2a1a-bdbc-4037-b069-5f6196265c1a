"""
🧪 إنشاء بيانات تجريبية شاملة - نظام كريستال دايموند
Create Comprehensive Test Data for Crestal Diamond System
"""

from database_operations import customer_ops, invoice_ops, inventory_ops, transaction_ops
from datetime import datetime, date, timedelta
import random
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_test_customers():
    """إنشاء عملاء تجريبيين متنوعين"""
    customers = [
        {
            'customer_name': 'أحمد محمد السيد',
            'phone': '01012345678',
            'address': 'القاهرة - مصر الجديدة - شارع الحجاز',
            'email': '<EMAIL>',
            'notes': 'عميل VIP - يفضل الذهب عيار 21 والألماس'
        },
        {
            'customer_name': 'فاطمة حسن علي',
            'phone': '01123456789',
            'address': 'الجيزة - المهندسين - شارع جامعة الدول',
            'email': '<EMAIL>',
            'notes': 'تفضل المجوهرات الكلاسيكية والأحجار الكريمة'
        },
        {
            'customer_name': 'محمد عبدالله أحمد',
            'phone': '01234567890',
            'address': 'الإسكندرية - سيدي جابر - كورنيش البحر',
            'email': '<EMAIL>',
            'notes': 'يشتري للمناسبات الخاصة - عميل موسمي'
        },
        {
            'customer_name': 'سارة أحمد محمود',
            'phone': '01098765432',
            'address': 'القاهرة - الزمالك - شارع 26 يوليو',
            'email': '<EMAIL>',
            'notes': 'تحب التصاميم العصرية والمودرن'
        },
        {
            'customer_name': 'علي حسام الدين',
            'phone': '01187654321',
            'address': 'الجيزة - الدقي - شارع التحرير',
            'email': '<EMAIL>',
            'notes': 'عميل منتظم - يشتري شهرياً - تاجر مجوهرات'
        },
        {
            'customer_name': 'نور الهدى إبراهيم',
            'phone': '01156789012',
            'address': 'القاهرة - مدينة نصر - الحي الأول',
            'email': '<EMAIL>',
            'notes': 'تفضل الذهب الأبيض والبلاتين'
        },
        {
            'customer_name': 'عمر خالد محمد',
            'phone': '01267890123',
            'address': 'الجيزة - الهرم - شارع الهرم الرئيسي',
            'email': '<EMAIL>',
            'notes': 'يشتري هدايا للعائلة - عميل مناسبات'
        },
        {
            'customer_name': 'ياسمين أحمد فتحي',
            'phone': '01378901234',
            'address': 'القاهرة - المعادي - شارع 9',
            'email': '<EMAIL>',
            'notes': 'تحب الأقراط والسلاسل الطويلة'
        }
    ]
    
    added_count = 0
    for customer in customers:
        if customer_ops.add_customer(customer):
            added_count += 1
            logger.info(f"✅ تم إضافة العميل: {customer['customer_name']}")
        else:
            logger.warning(f"⚠️ العميل موجود مسبقاً: {customer['customer_name']}")
    
    logger.info(f"📊 تم إضافة {added_count} عميل جديد")
    return added_count

def create_test_inventory():
    """إنشاء مخزون تجريبي متنوع"""
    inventory_items = [
        {
            'item_name': 'ذهب عيار 24 - سبائك مصرية',
            'category': 'ذهب خام',
            'description': 'سبائك ذهب عيار 24 من دار الضرب المصرية للتصنيع',
            'quantity': 100,
            'unit_price_usd': 65.50,
            'unit_price_egp': 2030.0,
            'total_value_usd': 6550.0,
            'total_value_egp': 203000.0,
            'supplier': 'دار الضرب المصرية',
            'location': 'خزنة رئيسية - الطابق الأول',
            'minimum_stock': 20,
            'notes': 'للتصنيع والتشكيل - جودة عالية'
        },
        {
            'item_name': 'ذهب عيار 21 - قطع جاهزة',
            'category': 'ذهب مشغول',
            'description': 'قطع ذهب عيار 21 جاهزة للبيع - تشكيلة متنوعة',
            'quantity': 50,
            'unit_price_usd': 58.75,
            'unit_price_egp': 1820.0,
            'total_value_usd': 2937.50,
            'total_value_egp': 91000.0,
            'supplier': 'ورشة كريستال الذهبية',
            'location': 'صالة العرض الرئيسية',
            'minimum_stock': 10,
            'notes': 'قطع جاهزة للعملاء - تصاميم حديثة'
        },
        {
            'item_name': 'ذهب عيار 18 - خواتم وأساور',
            'category': 'مجوهرات',
            'description': 'خواتم وأساور ذهب عيار 18 بتصاميم متنوعة',
            'quantity': 30,
            'unit_price_usd': 45.25,
            'unit_price_egp': 1400.0,
            'total_value_usd': 1357.50,
            'total_value_egp': 42000.0,
            'supplier': 'ورشة الفن الذهبي',
            'location': 'صالة العرض - قسم الخواتم',
            'minimum_stock': 5,
            'notes': 'تشكيلة متنوعة من الخواتم والأساور'
        },
        {
            'item_name': 'أحجار كريمة - زمرد كولومبي',
            'category': 'أحجار كريمة',
            'description': 'أحجار زمرد طبيعية من كولومبيا عالية الجودة',
            'quantity': 15,
            'unit_price_usd': 250.0,
            'unit_price_egp': 7750.0,
            'total_value_usd': 3750.0,
            'total_value_egp': 116250.0,
            'supplier': 'شركة الأحجار الكريمة الدولية',
            'location': 'خزنة خاصة - مكيفة',
            'minimum_stock': 3,
            'notes': 'أحجار عالية الجودة للقطع المميزة - شهادة أصالة'
        },
        {
            'item_name': 'أحجار كريمة - ياقوت بورمي',
            'category': 'أحجار كريمة',
            'description': 'أحجار ياقوت أحمر وأزرق من بورما',
            'quantity': 12,
            'unit_price_usd': 350.0,
            'unit_price_egp': 10850.0,
            'total_value_usd': 4200.0,
            'total_value_egp': 130200.0,
            'supplier': 'شركة الأحجار الكريمة الدولية',
            'location': 'خزنة خاصة - مكيفة',
            'minimum_stock': 2,
            'notes': 'أحجار نادرة للقطع الفاخرة - شهادة دولية'
        },
        {
            'item_name': 'ألماس طبيعي - قطع مختلفة',
            'category': 'ألماس',
            'description': 'ألماس طبيعي بقطع مختلفة (دائري، أميرة، قلب)',
            'quantity': 25,
            'unit_price_usd': 800.0,
            'unit_price_egp': 24800.0,
            'total_value_usd': 20000.0,
            'total_value_egp': 620000.0,
            'supplier': 'شركة الألماس العالمية',
            'location': 'خزنة فائقة الأمان',
            'minimum_stock': 5,
            'notes': 'ألماس معتمد دولياً - شهادات GIA'
        },
        {
            'item_name': 'لؤلؤ طبيعي - خليجي',
            'category': 'لؤلؤ',
            'description': 'لؤلؤ طبيعي من الخليج العربي بأحجام متنوعة',
            'quantity': 40,
            'unit_price_usd': 150.0,
            'unit_price_egp': 4650.0,
            'total_value_usd': 6000.0,
            'total_value_egp': 186000.0,
            'supplier': 'تجار اللؤلؤ الخليجي',
            'location': 'خزنة متوسطة الأمان',
            'minimum_stock': 8,
            'notes': 'لؤلؤ طبيعي عالي الجودة - تراث خليجي'
        }
    ]
    
    added_count = 0
    for item in inventory_items:
        if inventory_ops.add_inventory_item(item):
            added_count += 1
            logger.info(f"✅ تم إضافة عنصر المخزون: {item['item_name']}")
        else:
            logger.warning(f"⚠️ عنصر المخزون موجود مسبقاً: {item['item_name']}")
    
    logger.info(f"📦 تم إضافة {added_count} عنصر مخزون جديد")
    return added_count

def create_test_invoices():
    """إنشاء فواتير تجريبية واقعية"""
    base_date = date.today() - timedelta(days=60)
    
    # أسماء العملاء
    customers = [
        'أحمد محمد السيد', 'فاطمة حسن علي', 'محمد عبدالله أحمد', 
        'سارة أحمد محمود', 'علي حسام الدين', 'نور الهدى إبراهيم',
        'عمر خالد محمد', 'ياسمين أحمد فتحي'
    ]
    
    # منتجات متنوعة
    products = [
        'خاتم ذهب عيار 21 بحجر زمرد', 'سلسلة ذهب عيار 18 بقلادة ألماس',
        'أقراط ذهب عيار 21 بلؤلؤ طبيعي', 'سوار ذهب عيار 18 محفور',
        'دبلة زواج ذهب عيار 21', 'خاتم خطوبة بألماس سوليتير',
        'طقم ذهب كامل (سلسلة + أقراط + خاتم)', 'ساعة ذهب عيار 18',
        'خلخال ذهب عيار 21 مرصع بالزركون', 'بروش ذهب بأحجار كريمة'
    ]
    
    invoices = []
    
    for i in range(20):  # إنشاء 20 فاتورة
        invoice_date = base_date + timedelta(days=random.randint(0, 60))
        customer = random.choice(customers)
        product = random.choice(products)
        
        # أسعار واقعية متنوعة
        base_price_usd = random.randint(200, 1500)
        base_price_egp = base_price_usd * random.randint(30, 32)
        
        # أحجار الورشة
        workshop_stones_weight = round(random.uniform(0.1, 2.0), 3)
        workshop_stones_count = random.randint(1, 8)
        workshop_stones_price_usd = round(random.uniform(50, 300), 2)
        workshop_stones_price_egp = workshop_stones_price_usd * random.randint(30, 32)
        
        # أحجار العميل
        customer_stones_weight = round(random.uniform(0.0, 1.5), 3)
        customer_stones_count = random.randint(0, 5)
        customer_stones_installation_egp = round(random.uniform(0, 500), 2)
        
        # خدمات إضافية
        additional_services_total_egp = round(random.uniform(0, 300), 2)
        
        # حساب المجاميع
        subtotal_usd = base_price_usd + workshop_stones_price_usd
        subtotal_egp = base_price_egp + workshop_stones_price_egp + customer_stones_installation_egp + additional_services_total_egp
        
        # خصم عشوائي
        discount_percentage = random.choice([0, 5, 10, 15])
        discount_amount_usd = subtotal_usd * discount_percentage / 100
        discount_amount_egp = subtotal_egp * discount_percentage / 100
        
        total_usd = subtotal_usd - discount_amount_usd
        total_egp = subtotal_egp - discount_amount_egp
        
        # دفعات عشوائية
        payment_ratio = random.uniform(0.3, 1.0)
        paid_amount_usd = total_usd * payment_ratio
        paid_amount_egp = total_egp * payment_ratio
        
        invoice = {
            'invoice_number': f'TEST-{datetime.now().strftime("%Y%m%d")}-{i+1:03d}',
            'customer_name': customer,
            'phone': f'010{random.randint(10000000, 99999999)}',
            'address': 'عنوان العميل التجريبي',
            'main_product': product,
            'main_product_price_usd': base_price_usd,
            'main_product_price_egp': base_price_egp,
            'workshop_stones_weight': workshop_stones_weight,
            'workshop_stones_count': workshop_stones_count,
            'workshop_stones_price_usd': workshop_stones_price_usd,
            'workshop_stones_price_egp': workshop_stones_price_egp,
            'customer_stones_weight': customer_stones_weight,
            'customer_stones_count': customer_stones_count,
            'customer_stones_installation_egp': customer_stones_installation_egp,
            'additional_services': 'تنظيف وتلميع' if additional_services_total_egp > 0 else '',
            'additional_services_total_egp': additional_services_total_egp,
            'subtotal_usd': subtotal_usd,
            'subtotal_egp': subtotal_egp,
            'discount_percentage': discount_percentage,
            'discount_amount_usd': discount_amount_usd,
            'discount_amount_egp': discount_amount_egp,
            'total_usd': total_usd,
            'total_egp': total_egp,
            'payment_method': random.choice(['نقدي', 'بطاقة ائتمان', 'تحويل بنكي', 'شيك']),
            'paid_amount_usd': paid_amount_usd,
            'paid_amount_egp': paid_amount_egp,
            'remaining_amount_usd': total_usd - paid_amount_usd,
            'remaining_amount_egp': total_egp - paid_amount_egp,
            'notes': f'فاتورة تجريبية رقم {i+1} - تم إنشاؤها تلقائياً',
            'invoice_date': invoice_date,
            'status': random.choice(['completed', 'pending', 'paid', 'partial_paid'])
        }
        
        invoices.append(invoice)
    
    added_count = 0
    for invoice in invoices:
        if invoice_ops.add_invoice(invoice):
            added_count += 1
            logger.info(f"✅ تم إضافة الفاتورة: {invoice['invoice_number']}")
        else:
            logger.warning(f"⚠️ الفاتورة موجودة مسبقاً: {invoice['invoice_number']}")
    
    logger.info(f"🧾 تم إضافة {added_count} فاتورة جديدة")
    return added_count

def create_test_transactions():
    """إنشاء معاملات تجريبية متنوعة"""
    customers = [
        'أحمد محمد السيد', 'فاطمة حسن علي', 'محمد عبدالله أحمد', 
        'سارة أحمد محمود', 'علي حسام الدين'
    ]
    
    transactions = []
    base_date = date.today() - timedelta(days=30)
    
    for i in range(15):  # إنشاء 15 معاملة
        transaction_date = base_date + timedelta(days=random.randint(0, 30))
        customer = random.choice(customers)
        
        # أنواع معاملات مختلفة
        transaction_types = [
            ('payment', 'دفعة نقدية', -1),
            ('invoice', 'فاتورة جديدة', 1),
            ('adjustment', 'تسوية حساب', -1),
            ('opening_balance', 'رصيد افتتاحي', 1)
        ]
        
        transaction_type, description, multiplier = random.choice(transaction_types)
        
        amount_usd = round(random.uniform(50, 800) * multiplier, 2)
        amount_egp = round(amount_usd * random.randint(30, 32), 2)
        
        transaction = {
            'customer_name': customer,
            'transaction_date': transaction_date,
            'description': f'{description} - معاملة تجريبية {i+1}',
            'amount_usd': amount_usd,
            'amount_egp': amount_egp,
            'transaction_type': transaction_type
        }
        
        transactions.append(transaction)
    
    added_count = 0
    for transaction in transactions:
        if transaction_ops.add_transaction(transaction):
            added_count += 1
            logger.info(f"✅ تم إضافة المعاملة: {transaction['description']}")
        else:
            logger.warning(f"⚠️ فشل في إضافة المعاملة: {transaction['description']}")
    
    logger.info(f"💳 تم إضافة {added_count} معاملة جديدة")
    return added_count

def main():
    """تشغيل إنشاء البيانات التجريبية الشاملة"""
    logger.info("🚀 بدء إنشاء البيانات التجريبية الشاملة...")
    logger.info("=" * 60)
    
    results = {}
    
    # إنشاء العملاء
    logger.info("👥 إنشاء العملاء التجريبيين...")
    results['customers'] = create_test_customers()
    
    # إنشاء المخزون
    logger.info("📦 إنشاء المخزون التجريبي...")
    results['inventory'] = create_test_inventory()
    
    # إنشاء الفواتير
    logger.info("🧾 إنشاء الفواتير التجريبية...")
    results['invoices'] = create_test_invoices()
    
    # إنشاء المعاملات
    logger.info("💳 إنشاء المعاملات التجريبية...")
    results['transactions'] = create_test_transactions()
    
    # تقرير النتائج
    logger.info("=" * 60)
    logger.info("📊 تقرير إنشاء البيانات التجريبية:")
    logger.info(f"   👥 العملاء الجدد: {results['customers']}")
    logger.info(f"   📦 عناصر المخزون الجديدة: {results['inventory']}")
    logger.info(f"   🧾 الفواتير الجديدة: {results['invoices']}")
    logger.info(f"   💳 المعاملات الجديدة: {results['transactions']}")
    
    total_added = sum(results.values())
    logger.info(f"   🎯 الإجمالي: {total_added} عنصر جديد")
    
    if total_added > 0:
        logger.info("🎉 تم إنشاء البيانات التجريبية بنجاح!")
        logger.info("💡 يمكنك الآن اختبار جميع وظائف النظام بالبيانات الجديدة")
        return True
    else:
        logger.warning("⚠️ لم يتم إضافة أي بيانات جديدة")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🏁 انتهت عملية إنشاء البيانات التجريبية بنجاح!")
        print("🔗 افتح التطبيق على: http://localhost:8501")
    else:
        print("\n🚨 انتهت عملية إنشاء البيانات مع وجود مشاكل!")
