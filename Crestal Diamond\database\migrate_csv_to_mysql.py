"""
🔄 تهجير البيانات من CSV إلى MySQL - نظام كريستال دايموند
Data Migration from CSV to MySQL for Crestal Diamond System
"""

import pandas as pd
import os
from database_config import db_config
from database_operations import customer_ops, invoice_ops, inventory_ops
from create_tables import create_all_tables
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class DataMigration:
    """فئة تهجير البيانات"""
    
    def __init__(self):
        self.csv_files = {
            'customers': 'customers.csv',
            'invoices': 'invoices.csv', 
            'inventory': 'gold_inventory.csv'
        }
    
    def check_csv_files(self):
        """فحص وجود ملفات CSV"""
        existing_files = []
        missing_files = []
        
        for file_type, filename in self.csv_files.items():
            if os.path.exists(filename):
                existing_files.append((file_type, filename))
                logger.info(f"✅ تم العثور على ملف: {filename}")
            else:
                missing_files.append((file_type, filename))
                logger.warning(f"⚠️ ملف غير موجود: {filename}")
        
        return existing_files, missing_files
    
    def migrate_customers(self):
        """تهجير بيانات العملاء"""
        filename = self.csv_files['customers']
        if not os.path.exists(filename):
            logger.warning(f"⚠️ ملف العملاء غير موجود: {filename}")
            return False
        
        try:
            df = pd.read_csv(filename)
            logger.info(f"📊 تم قراءة {len(df)} عميل من ملف CSV")
            
            migrated_count = 0
            for _, row in df.iterrows():
                customer_data = {
                    'customer_name': row.get('customer_name', ''),
                    'phone': row.get('phone', ''),
                    'address': row.get('address', ''),
                    'email': row.get('email', ''),
                    'notes': row.get('notes', '')
                }
                
                if customer_ops.add_customer(customer_data):
                    migrated_count += 1
                else:
                    logger.error(f"❌ فشل في تهجير العميل: {customer_data['customer_name']}")
            
            logger.info(f"✅ تم تهجير {migrated_count} عميل بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهجير العملاء: {e}")
            return False
    
    def migrate_invoices(self):
        """تهجير بيانات الفواتير"""
        filename = self.csv_files['invoices']
        if not os.path.exists(filename):
            logger.warning(f"⚠️ ملف الفواتير غير موجود: {filename}")
            return False
        
        try:
            df = pd.read_csv(filename)
            logger.info(f"📊 تم قراءة {len(df)} فاتورة من ملف CSV")
            
            migrated_count = 0
            for _, row in df.iterrows():
                # تحويل التواريخ
                invoice_date = None
                delivery_date = None
                
                try:
                    if pd.notna(row.get('invoice_date')):
                        invoice_date = pd.to_datetime(row['invoice_date']).date()
                except:
                    invoice_date = datetime.now().date()
                
                try:
                    if pd.notna(row.get('delivery_date')):
                        delivery_date = pd.to_datetime(row['delivery_date']).date()
                except:
                    delivery_date = None
                
                invoice_data = {
                    'invoice_number': row.get('invoice_number', ''),
                    'customer_name': row.get('customer_name', ''),
                    'phone': row.get('phone', ''),
                    'address': row.get('address', ''),
                    'main_product': row.get('main_product', ''),
                    'main_product_price_usd': float(row.get('main_product_price_usd', 0) or 0),
                    'main_product_price_egp': float(row.get('main_product_price_egp', 0) or 0),
                    'workshop_stones_weight': float(row.get('workshop_stones_weight', 0) or 0),
                    'workshop_stones_count': int(row.get('workshop_stones_count', 0) or 0),
                    'workshop_stones_price_usd': float(row.get('workshop_stones_price_usd', 0) or 0),
                    'workshop_stones_price_egp': float(row.get('workshop_stones_price_egp', 0) or 0),
                    'customer_stones_weight': float(row.get('customer_stones_weight', 0) or 0),
                    'customer_stones_count': int(row.get('customer_stones_count', 0) or 0),
                    'customer_stones_installation_egp': float(row.get('customer_stones_installation_egp', 0) or 0),
                    'additional_services': row.get('additional_services', ''),
                    'additional_services_total_egp': float(row.get('additional_services_total_egp', 0) or 0),
                    'subtotal_usd': float(row.get('subtotal_usd', 0) or 0),
                    'subtotal_egp': float(row.get('subtotal_egp', 0) or 0),
                    'discount_percentage': float(row.get('discount_percentage', 0) or 0),
                    'discount_amount_usd': float(row.get('discount_amount_usd', 0) or 0),
                    'discount_amount_egp': float(row.get('discount_amount_egp', 0) or 0),
                    'total_usd': float(row.get('total_usd', 0) or 0),
                    'total_egp': float(row.get('total_egp', 0) or 0),
                    'payment_method': row.get('payment_method', ''),
                    'paid_amount_usd': float(row.get('paid_amount_usd', 0) or 0),
                    'paid_amount_egp': float(row.get('paid_amount_egp', 0) or 0),
                    'remaining_amount_usd': float(row.get('remaining_amount_usd', 0) or 0),
                    'remaining_amount_egp': float(row.get('remaining_amount_egp', 0) or 0),
                    'notes': row.get('notes', ''),
                    'invoice_date': invoice_date,
                    'delivery_date': delivery_date,
                    'status': row.get('status', 'pending')
                }
                
                if invoice_ops.add_invoice(invoice_data):
                    migrated_count += 1
                else:
                    logger.error(f"❌ فشل في تهجير الفاتورة: {invoice_data['invoice_number']}")
            
            logger.info(f"✅ تم تهجير {migrated_count} فاتورة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهجير الفواتير: {e}")
            return False
    
    def migrate_inventory(self):
        """تهجير بيانات المخزون"""
        filename = self.csv_files['inventory']
        if not os.path.exists(filename):
            logger.warning(f"⚠️ ملف المخزون غير موجود: {filename}")
            return False
        
        try:
            df = pd.read_csv(filename)
            logger.info(f"📊 تم قراءة {len(df)} عنصر من ملف المخزون CSV")
            
            migrated_count = 0
            for _, row in df.iterrows():
                item_data = {
                    'item_name': row.get('item_name', ''),
                    'category': row.get('category', 'ذهب'),
                    'description': row.get('description', ''),
                    'quantity': int(row.get('quantity', 0) or 0),
                    'unit_price_usd': float(row.get('unit_price_usd', 0) or 0),
                    'unit_price_egp': float(row.get('unit_price_egp', 0) or 0),
                    'total_value_usd': float(row.get('total_value_usd', 0) or 0),
                    'total_value_egp': float(row.get('total_value_egp', 0) or 0),
                    'supplier': row.get('supplier', ''),
                    'location': row.get('location', ''),
                    'minimum_stock': int(row.get('minimum_stock', 0) or 0),
                    'notes': row.get('notes', '')
                }
                
                if inventory_ops.add_inventory_item(item_data):
                    migrated_count += 1
                else:
                    logger.error(f"❌ فشل في تهجير عنصر المخزون: {item_data['item_name']}")
            
            logger.info(f"✅ تم تهجير {migrated_count} عنصر مخزون بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهجير المخزون: {e}")
            return False
    
    def run_full_migration(self):
        """تشغيل التهجير الكامل"""
        logger.info("🚀 بدء عملية التهجير الكاملة...")
        
        # إنشاء الجداول أولاً
        if not create_all_tables():
            logger.error("❌ فشل في إنشاء الجداول")
            return False
        
        # فحص ملفات CSV
        existing_files, missing_files = self.check_csv_files()
        
        if not existing_files:
            logger.warning("⚠️ لا توجد ملفات CSV للتهجير")
            return False
        
        # تهجير البيانات
        migration_results = {}
        
        for file_type, filename in existing_files:
            if file_type == 'customers':
                migration_results['customers'] = self.migrate_customers()
            elif file_type == 'invoices':
                migration_results['invoices'] = self.migrate_invoices()
            elif file_type == 'inventory':
                migration_results['inventory'] = self.migrate_inventory()
        
        # تقرير النتائج
        successful_migrations = sum(migration_results.values())
        total_migrations = len(migration_results)
        
        logger.info(f"📊 تقرير التهجير: {successful_migrations}/{total_migrations} نجح")
        
        return successful_migrations > 0

def main():
    """الدالة الرئيسية للتهجير"""
    migration = DataMigration()
    return migration.run_full_migration()

if __name__ == "__main__":
    main()
