"""
💎 Crestal Diamond - Modern UI Version
نظام إدارة ورشة المجوهرات - الإصدار الحديث مع Shadcn UI
"""

import streamlit as st
import sys
import os
import pandas as pd
from datetime import datetime

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

# استيراد وحدات قاعدة البيانات
try:
    from database.database_config import test_connection
    from database.database_operations import customer_ops, invoice_ops, inventory_ops
    DATABASE_AVAILABLE = True
except ImportError as e:
    st.error(f"خطأ في تحميل قاعدة البيانات: {e}")
    DATABASE_AVAILABLE = False

# إعداد الصفحة
st.set_page_config(
    page_title="💎 Crestal Diamond - Modern",
    page_icon="💎",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS مخصص للتصميم الحديث
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }
    
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    
    .feature-card {
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 0.5rem;
    }
    
    .status-success {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-warning {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .sidebar-logo {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: -1rem -1rem 2rem -1rem;
        border-radius: 0 0 15px 15px;
    }
</style>
""", unsafe_allow_html=True)

def custom_alert(title, description, variant="info"):
    """مكون تنبيه مخصص"""
    colors = {
        "info": {"bg": "#d1ecf1", "border": "#bee5eb", "text": "#0c5460"},
        "success": {"bg": "#d4edda", "border": "#c3e6cb", "text": "#155724"},
        "warning": {"bg": "#fff3cd", "border": "#ffeaa7", "text": "#856404"},
        "error": {"bg": "#f8d7da", "border": "#f5c6cb", "text": "#721c24"}
    }

    color = colors.get(variant, colors["info"])

    st.markdown(f"""
    <div style="
        background-color: {color['bg']};
        border: 1px solid {color['border']};
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        color: {color['text']};
    ">
        <strong>{title}</strong><br>
        {description}
    </div>
    """, unsafe_allow_html=True)

def custom_metric_card(title, value, description):
    """بطاقة مقياس مخصصة"""
    st.markdown(f"""
    <div class="metric-card">
        <h3 style="margin: 0; color: #667eea;">{title}</h3>
        <h2 style="margin: 0.5rem 0; color: #2c3e50;">{value}</h2>
        <p style="margin: 0; color: #6c757d; font-size: 0.9rem;">{description}</p>
    </div>
    """, unsafe_allow_html=True)

def main_dashboard():
    """لوحة التحكم الرئيسية الحديثة"""

    # العنوان الرئيسي
    st.markdown("""
    <div class="main-header">
        <h1>💎 مرحباً بك في نظام Crestal Diamond</h1>
        <p>الإصدار الحديث 3.0 - مدعوم بقاعدة بيانات MySQL وتصميم حديث</p>
    </div>
    """, unsafe_allow_html=True)

    # حالة قاعدة البيانات
    col1, col2 = st.columns([3, 1])

    with col1:
        if DATABASE_AVAILABLE:
            if test_connection():
                custom_alert(
                    "🗄️ قاعدة البيانات",
                    "MySQL متصلة ومتاحة",
                    "success"
                )
            else:
                custom_alert(
                    "❌ خطأ في قاعدة البيانات",
                    "فشل في الاتصال بقاعدة البيانات",
                    "error"
                )
        else:
            custom_alert(
                "⚠️ تحذير",
                "قاعدة البيانات غير متاحة - الوضع التقليدي",
                "warning"
            )

    with col2:
        # زر إعادة تحديث
        if st.button("🔄 تحديث", key="refresh_btn"):
            st.rerun()
    
    st.divider()
    
    # إحصائيات سريعة بتصميم حديث
    if DATABASE_AVAILABLE:
        st.subheader("📊 إحصائيات سريعة")
        
        try:
            # جلب البيانات
            customers_df = customer_ops.get_all_customers()
            invoices_df = invoice_ops.get_all_invoices()
            inventory_df = inventory_ops.get_all_inventory()
            
            # حساب الإحصائيات
            total_customers = len(customers_df) if not customers_df.empty else 0
            total_invoices = len(invoices_df) if not invoices_df.empty else 0
            total_sales_usd = invoices_df['total_usd'].sum() if not invoices_df.empty else 0
            total_sales_egp = invoices_df['total_egp'].sum() if not invoices_df.empty else 0
            total_inventory_items = len(inventory_df) if not inventory_df.empty else 0
            total_inventory_value_usd = inventory_df['total_value_usd'].sum() if not inventory_df.empty else 0
            
            # عرض البطاقات
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                custom_metric_card(
                    "👥 العملاء",
                    str(total_customers),
                    "إجمالي العملاء المسجلين"
                )

            with col2:
                custom_metric_card(
                    "🧾 الفواتير",
                    str(total_invoices),
                    "إجمالي الفواتير المُنشأة"
                )

            with col3:
                custom_metric_card(
                    "💰 المبيعات (USD)",
                    f"${total_sales_usd:,.2f}",
                    "إجمالي المبيعات بالدولار"
                )

            with col4:
                custom_metric_card(
                    "📦 المخزون",
                    str(total_inventory_items),
                    "عناصر المخزون المتاحة"
                )

            # بطاقات إضافية
            col5, col6 = st.columns(2)

            with col5:
                custom_metric_card(
                    "💵 المبيعات (EGP)",
                    f"{total_sales_egp:,.2f} ج.م",
                    "إجمالي المبيعات بالجنيه المصري"
                )

            with col6:
                custom_metric_card(
                    "🏪 قيمة المخزون",
                    f"${total_inventory_value_usd:,.2f}",
                    "القيمة الإجمالية للمخزون"
                )
                
        except Exception as e:
            custom_alert(
                "خطأ في جلب الإحصائيات",
                str(e),
                "error"
            )
    
    st.divider()
    
    # الميزات الرئيسية
    st.subheader("🚀 الميزات الرئيسية")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h3>📝 إنشاء الفواتير</h3>
            <p>• فواتير احترافية للذهب والألماس</p>
            <p>• حساب المصنعية بدقة</p>
            <p>• إدارة الأحجار الكريمة</p>
            <p>• الخدمات الإضافية</p>
            <p><strong>🆕 حفظ في قاعدة البيانات MySQL</strong></p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("🚀 إنشاء فاتورة جديدة", key="create_invoice_btn", use_container_width=True):
            st.switch_page("pages/invoice.py")

    with col2:
        st.markdown("""
        <div class="feature-card">
            <h3>👥 إدارة العملاء</h3>
            <p>• حسابات العملاء التفصيلية</p>
            <p>• تتبع المدفوعات والأرصدة</p>
            <p>• كشوف حساب شاملة</p>
            <p>• تاريخ المعاملات</p>
            <p><strong>🆕 قاعدة بيانات متقدمة</strong></p>
        </div>
        """, unsafe_allow_html=True)

        if st.button("📊 إدارة العملاء", key="manage_customers_btn", use_container_width=True):
            st.switch_page("pages/01_customers_data.py")

    with col3:
        st.markdown("""
        <div class="feature-card">
            <h3>📦 إدارة المخزون</h3>
            <p>• مخزون الذهب والأحجار الكريمة</p>
            <p>• تتبع الكميات والأسعار</p>
            <p>• تقارير المخزون المتقدمة</p>
            <p>• إحصائيات تفاعلية</p>
            <p><strong>🆕 تحليلات MySQL</strong></p>
        </div>
        """, unsafe_allow_html=True)

        if st.button("🏪 إدارة المخزون", key="manage_inventory_btn", use_container_width=True):
            st.switch_page("pages/02_gold_inventory.py")

def sidebar_content():
    """محتوى الشريط الجانبي الحديث"""
    
    # شعار النظام
    st.markdown("""
    <div class="sidebar-logo">
        <h2 style="color: white; margin: 0;">💎 Crestal Diamond</h2>
        <p style="color: #e9ecef; margin: 0; font-size: 0.9rem;">الإصدار الحديث 3.0</p>
    </div>
    """, unsafe_allow_html=True)
    
    # معلومات النظام
    st.markdown("### 📋 معلومات النظام")
    
    # حالة النظام
    if DATABASE_AVAILABLE:
        st.markdown('<span class="status-badge status-success">✅ قاعدة البيانات نشطة</span>', unsafe_allow_html=True)
    else:
        st.markdown('<span class="status-badge status-warning">⚠️ وضع تقليدي</span>', unsafe_allow_html=True)
    
    st.markdown('<span class="status-badge status-success">🎨 Shadcn UI</span>', unsafe_allow_html=True)
    st.markdown('<span class="status-badge status-success">🗄️ MySQL</span>', unsafe_allow_html=True)
    
    st.divider()
    
    # الوظائف السريعة
    st.markdown("### ⚡ وظائف سريعة")
    
    if st.button("🧪 اختبار قاعدة البيانات", key="test_db_btn", use_container_width=True):
        st.switch_page("database/test_database.py")

    if st.button("📊 عرض الإحصائيات", key="stats_btn", use_container_width=True):
        st.rerun()
    
    st.divider()
    
    # معلومات التطوير
    st.markdown("### 🛠️ التقنيات المستخدمة")
    st.info("""
    **🔧 التقنيات:**
    - Python 3.13
    - Streamlit 1.46+
    - MySQL 8.0+
    - Shadcn UI
    - Pandas & NumPy
    
    **🎨 التصميم:**
    - واجهة حديثة
    - تصميم متجاوب
    - ألوان متدرجة
    - تأثيرات تفاعلية
    """)

# تشغيل التطبيق
def main():
    """الدالة الرئيسية"""
    
    # الشريط الجانبي
    with st.sidebar:
        sidebar_content()
    
    # المحتوى الرئيسي
    main_dashboard()
    
    # تذييل الصفحة
    st.divider()
    st.markdown("""
    <div style="text-align: center; padding: 2rem; color: #6c757d;">
        <p>💎 <strong>Crestal Diamond</strong> - حيث تلتقي التكنولوجيا بالجمال</p>
        <p style="font-size: 0.8rem;">الإصدار 3.0 - آخر تحديث: {}</p>
    </div>
    """.format(datetime.now().strftime("%Y-%m-%d")), unsafe_allow_html=True)

if __name__ == "__main__":
    main()
